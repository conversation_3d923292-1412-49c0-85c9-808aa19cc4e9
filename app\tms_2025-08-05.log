2025-08-05 10:35:39 [WARNING] src.views.main_window:1927 - tabMoved信号不可用，将使用替代方案监控标签页顺序
2025-08-05 10:35:39 [WARNING] src.views.problem_editor_dock:592 - Master field list is empty. Cannot reliably apply group settings.
2025-08-05 10:35:59 [WARNING] src.utils.chart_renderer:1832 - 词云图添加数据失败，尝试最基本参数: too many values to unpack (expected 2)
2025-08-05 10:35:59 [WARNING] src.utils.chart_renderer:1832 - 词云图添加数据失败，尝试最基本参数: too many values to unpack (expected 2)
2025-08-05 10:45:25 [WARNING] src.views.main_window:1927 - tabMoved信号不可用，将使用替代方案监控标签页顺序
2025-08-05 10:45:25 [WARNING] src.views.problem_editor_dock:592 - Master field list is empty. Cannot reliably apply group settings.
2025-08-05 10:45:45 [WARNING] src.utils.chart_renderer:1832 - 词云图添加数据失败，尝试最基本参数: too many values to unpack (expected 2)
2025-08-05 10:45:45 [WARNING] src.utils.chart_renderer:1832 - 词云图添加数据失败，尝试最基本参数: too many values to unpack (expected 2)
2025-08-05 11:01:51 [WARNING] src.views.main_window:1927 - tabMoved信号不可用，将使用替代方案监控标签页顺序
2025-08-05 11:01:51 [WARNING] src.views.problem_editor_dock:592 - Master field list is empty. Cannot reliably apply group settings.
2025-08-05 11:02:08 [WARNING] src.utils.chart_renderer:1832 - 词云图添加数据失败，尝试最基本参数: too many values to unpack (expected 2)
2025-08-05 11:02:08 [WARNING] src.utils.chart_renderer:1832 - 词云图添加数据失败，尝试最基本参数: too many values to unpack (expected 2)
2025-08-05 11:13:56 [WARNING] src.views.main_window:1927 - tabMoved信号不可用，将使用替代方案监控标签页顺序
2025-08-05 11:13:56 [WARNING] src.views.problem_editor_dock:592 - Master field list is empty. Cannot reliably apply group settings.
2025-08-05 11:14:15 [WARNING] src.utils.chart_renderer:136 - ⚠️ 本地echarts资源未找到，但已强制设置为离线模式
2025-08-05 11:14:15 [WARNING] src.utils.chart_renderer:136 - ⚠️ 本地echarts资源未找到，但已强制设置为离线模式
2025-08-05 11:14:15 [WARNING] src.utils.chart_renderer:136 - ⚠️ 本地echarts资源未找到，但已强制设置为离线模式
2025-08-05 11:14:15 [WARNING] src.utils.chart_renderer:136 - ⚠️ 本地echarts资源未找到，但已强制设置为离线模式
2025-08-05 11:14:15 [WARNING] src.utils.chart_renderer:136 - ⚠️ 本地echarts资源未找到，但已强制设置为离线模式
2025-08-05 11:14:15 [WARNING] src.utils.chart_renderer:136 - ⚠️ 本地echarts资源未找到，但已强制设置为离线模式
2025-08-05 11:14:15 [WARNING] src.utils.chart_renderer:136 - ⚠️ 本地echarts资源未找到，但已强制设置为离线模式
2025-08-05 11:14:15 [WARNING] src.utils.chart_renderer:136 - ⚠️ 本地echarts资源未找到，但已强制设置为离线模式
2025-08-05 11:14:15 [WARNING] src.utils.chart_renderer:1884 - 词云图添加数据失败，尝试最基本参数: too many values to unpack (expected 2)
2025-08-05 11:14:15 [WARNING] src.utils.chart_renderer:136 - ⚠️ 本地echarts资源未找到，但已强制设置为离线模式
2025-08-05 11:14:16 [WARNING] src.utils.chart_renderer:136 - ⚠️ 本地echarts资源未找到，但已强制设置为离线模式
2025-08-05 11:14:16 [WARNING] src.utils.chart_renderer:1884 - 词云图添加数据失败，尝试最基本参数: too many values to unpack (expected 2)
2025-08-05 11:14:16 [WARNING] src.utils.chart_renderer:136 - ⚠️ 本地echarts资源未找到，但已强制设置为离线模式
2025-08-05 11:14:16 [WARNING] src.utils.chart_renderer:136 - ⚠️ 本地echarts资源未找到，但已强制设置为离线模式
2025-08-05 11:14:16 [WARNING] src.utils.chart_renderer:136 - ⚠️ 本地echarts资源未找到，但已强制设置为离线模式
2025-08-05 11:14:16 [WARNING] src.utils.chart_renderer:136 - ⚠️ 本地echarts资源未找到，但已强制设置为离线模式
2025-08-05 11:14:16 [WARNING] src.utils.chart_renderer:136 - ⚠️ 本地echarts资源未找到，但已强制设置为离线模式
2025-08-05 11:14:16 [WARNING] src.utils.chart_renderer:136 - ⚠️ 本地echarts资源未找到，但已强制设置为离线模式
2025-08-05 11:14:16 [WARNING] src.utils.chart_renderer:136 - ⚠️ 本地echarts资源未找到，但已强制设置为离线模式
2025-08-05 11:14:16 [WARNING] src.utils.chart_renderer:136 - ⚠️ 本地echarts资源未找到，但已强制设置为离线模式
2025-08-05 11:14:16 [WARNING] src.utils.chart_renderer:136 - ⚠️ 本地echarts资源未找到，但已强制设置为离线模式
2025-08-05 11:14:16 [WARNING] src.utils.chart_renderer:136 - ⚠️ 本地echarts资源未找到，但已强制设置为离线模式
2025-08-05 11:14:16 [WARNING] src.utils.chart_renderer:136 - ⚠️ 本地echarts资源未找到，但已强制设置为离线模式
2025-08-05 11:50:41 [WARNING] src.views.main_window:1927 - tabMoved信号不可用，将使用替代方案监控标签页顺序
2025-08-05 11:50:41 [WARNING] src.views.problem_editor_dock:592 - Master field list is empty. Cannot reliably apply group settings.
2025-08-05 11:50:57 [WARNING] src.utils.chart_renderer:136 - ⚠️ 本地echarts资源未找到，但已强制设置为离线模式
2025-08-05 11:50:57 [WARNING] src.utils.chart_renderer:136 - ⚠️ 本地echarts资源未找到，但已强制设置为离线模式
2025-08-05 11:50:57 [WARNING] src.utils.chart_renderer:136 - ⚠️ 本地echarts资源未找到，但已强制设置为离线模式
2025-08-05 11:50:57 [WARNING] src.utils.chart_renderer:136 - ⚠️ 本地echarts资源未找到，但已强制设置为离线模式
2025-08-05 11:50:57 [WARNING] src.utils.chart_renderer:136 - ⚠️ 本地echarts资源未找到，但已强制设置为离线模式
2025-08-05 11:50:57 [WARNING] src.utils.chart_renderer:136 - ⚠️ 本地echarts资源未找到，但已强制设置为离线模式
2025-08-05 11:50:57 [WARNING] src.utils.chart_renderer:136 - ⚠️ 本地echarts资源未找到，但已强制设置为离线模式
2025-08-05 11:50:57 [WARNING] src.utils.chart_renderer:136 - ⚠️ 本地echarts资源未找到，但已强制设置为离线模式
2025-08-05 11:50:57 [WARNING] src.utils.chart_renderer:1884 - 词云图添加数据失败，尝试最基本参数: too many values to unpack (expected 2)
2025-08-05 11:50:57 [WARNING] src.utils.chart_renderer:136 - ⚠️ 本地echarts资源未找到，但已强制设置为离线模式
2025-08-05 11:50:57 [WARNING] src.utils.chart_renderer:136 - ⚠️ 本地echarts资源未找到，但已强制设置为离线模式
2025-08-05 11:50:57 [WARNING] src.utils.chart_renderer:1884 - 词云图添加数据失败，尝试最基本参数: too many values to unpack (expected 2)
2025-08-05 11:50:57 [WARNING] src.utils.chart_renderer:136 - ⚠️ 本地echarts资源未找到，但已强制设置为离线模式
2025-08-05 11:50:57 [WARNING] src.utils.chart_renderer:136 - ⚠️ 本地echarts资源未找到，但已强制设置为离线模式
2025-08-05 11:50:58 [WARNING] src.utils.chart_renderer:136 - ⚠️ 本地echarts资源未找到，但已强制设置为离线模式
2025-08-05 11:50:58 [WARNING] src.utils.chart_renderer:136 - ⚠️ 本地echarts资源未找到，但已强制设置为离线模式
2025-08-05 11:50:58 [WARNING] src.utils.chart_renderer:136 - ⚠️ 本地echarts资源未找到，但已强制设置为离线模式
2025-08-05 11:50:58 [WARNING] src.utils.chart_renderer:136 - ⚠️ 本地echarts资源未找到，但已强制设置为离线模式
2025-08-05 11:50:58 [WARNING] src.utils.chart_renderer:136 - ⚠️ 本地echarts资源未找到，但已强制设置为离线模式
2025-08-05 11:50:58 [WARNING] src.utils.chart_renderer:136 - ⚠️ 本地echarts资源未找到，但已强制设置为离线模式
2025-08-05 11:50:58 [WARNING] src.utils.chart_renderer:136 - ⚠️ 本地echarts资源未找到，但已强制设置为离线模式
2025-08-05 11:50:58 [WARNING] src.utils.chart_renderer:136 - ⚠️ 本地echarts资源未找到，但已强制设置为离线模式
2025-08-05 11:50:58 [WARNING] src.utils.chart_renderer:136 - ⚠️ 本地echarts资源未找到，但已强制设置为离线模式
2025-08-05 11:57:17 [WARNING] src.views.main_window:1927 - tabMoved信号不可用，将使用替代方案监控标签页顺序
2025-08-05 11:57:17 [WARNING] src.views.problem_editor_dock:592 - Master field list is empty. Cannot reliably apply group settings.
2025-08-05 11:57:34 [WARNING] src.utils.chart_renderer:136 - ⚠️ 本地echarts资源未找到，但已强制设置为离线模式
2025-08-05 11:57:34 [WARNING] src.utils.chart_renderer:136 - ⚠️ 本地echarts资源未找到，但已强制设置为离线模式
2025-08-05 11:57:34 [WARNING] src.utils.chart_renderer:136 - ⚠️ 本地echarts资源未找到，但已强制设置为离线模式
2025-08-05 11:57:34 [WARNING] src.utils.chart_renderer:136 - ⚠️ 本地echarts资源未找到，但已强制设置为离线模式
2025-08-05 11:57:34 [WARNING] src.utils.chart_renderer:136 - ⚠️ 本地echarts资源未找到，但已强制设置为离线模式
2025-08-05 11:57:34 [WARNING] src.utils.chart_renderer:136 - ⚠️ 本地echarts资源未找到，但已强制设置为离线模式
2025-08-05 11:57:34 [WARNING] src.utils.chart_renderer:136 - ⚠️ 本地echarts资源未找到，但已强制设置为离线模式
2025-08-05 11:57:34 [WARNING] src.utils.chart_renderer:136 - ⚠️ 本地echarts资源未找到，但已强制设置为离线模式
2025-08-05 11:57:34 [WARNING] src.utils.chart_renderer:1884 - 词云图添加数据失败，尝试最基本参数: too many values to unpack (expected 2)
2025-08-05 11:57:34 [WARNING] src.utils.chart_renderer:136 - ⚠️ 本地echarts资源未找到，但已强制设置为离线模式
2025-08-05 11:57:34 [WARNING] src.utils.chart_renderer:136 - ⚠️ 本地echarts资源未找到，但已强制设置为离线模式
2025-08-05 11:57:34 [WARNING] src.utils.chart_renderer:1884 - 词云图添加数据失败，尝试最基本参数: too many values to unpack (expected 2)
2025-08-05 11:57:34 [WARNING] src.utils.chart_renderer:136 - ⚠️ 本地echarts资源未找到，但已强制设置为离线模式
2025-08-05 11:57:34 [WARNING] src.utils.chart_renderer:136 - ⚠️ 本地echarts资源未找到，但已强制设置为离线模式
2025-08-05 11:57:34 [WARNING] src.utils.chart_renderer:136 - ⚠️ 本地echarts资源未找到，但已强制设置为离线模式
2025-08-05 11:57:34 [WARNING] src.utils.chart_renderer:136 - ⚠️ 本地echarts资源未找到，但已强制设置为离线模式
2025-08-05 11:57:34 [WARNING] src.utils.chart_renderer:136 - ⚠️ 本地echarts资源未找到，但已强制设置为离线模式
2025-08-05 11:57:34 [WARNING] src.utils.chart_renderer:136 - ⚠️ 本地echarts资源未找到，但已强制设置为离线模式
2025-08-05 11:57:34 [WARNING] src.utils.chart_renderer:136 - ⚠️ 本地echarts资源未找到，但已强制设置为离线模式
2025-08-05 11:57:34 [WARNING] src.utils.chart_renderer:136 - ⚠️ 本地echarts资源未找到，但已强制设置为离线模式
2025-08-05 11:57:34 [WARNING] src.utils.chart_renderer:136 - ⚠️ 本地echarts资源未找到，但已强制设置为离线模式
2025-08-05 11:57:34 [WARNING] src.utils.chart_renderer:136 - ⚠️ 本地echarts资源未找到，但已强制设置为离线模式
2025-08-05 11:57:34 [WARNING] src.utils.chart_renderer:136 - ⚠️ 本地echarts资源未找到，但已强制设置为离线模式
2025-08-05 12:25:17 [WARNING] src.views.main_window:1927 - tabMoved信号不可用，将使用替代方案监控标签页顺序
2025-08-05 12:25:17 [WARNING] src.views.problem_editor_dock:592 - Master field list is empty. Cannot reliably apply group settings.
2025-08-05 12:37:19 [WARNING] src.views.main_window:1927 - tabMoved信号不可用，将使用替代方案监控标签页顺序
2025-08-05 12:37:19 [WARNING] src.views.problem_editor_dock:592 - Master field list is empty. Cannot reliably apply group settings.
2025-08-05 12:37:36 [WARNING] src.utils.chart_renderer:1909 - 词云图添加数据失败，尝试最基本参数: too many values to unpack (expected 2)
2025-08-05 12:37:37 [WARNING] src.utils.chart_renderer:1909 - 词云图添加数据失败，尝试最基本参数: too many values to unpack (expected 2)
2025-08-05 12:42:31 [WARNING] src.views.main_window:1927 - tabMoved信号不可用，将使用替代方案监控标签页顺序
2025-08-05 12:42:31 [WARNING] src.views.problem_editor_dock:592 - Master field list is empty. Cannot reliably apply group settings.
2025-08-05 12:42:48 [WARNING] src.utils.chart_renderer:1909 - 词云图添加数据失败，尝试最基本参数: too many values to unpack (expected 2)
2025-08-05 12:42:49 [WARNING] src.utils.chart_renderer:1909 - 词云图添加数据失败，尝试最基本参数: too many values to unpack (expected 2)
2025-08-05 13:01:32 [WARNING] src.views.main_window:1927 - tabMoved信号不可用，将使用替代方案监控标签页顺序
2025-08-05 13:01:32 [WARNING] src.views.problem_editor_dock:592 - Master field list is empty. Cannot reliably apply group settings.
2025-08-05 13:18:22 [WARNING] src.views.main_window:1927 - tabMoved信号不可用，将使用替代方案监控标签页顺序
2025-08-05 13:18:22 [WARNING] src.views.problem_editor_dock:592 - Master field list is empty. Cannot reliably apply group settings.
2025-08-05 13:18:40 [WARNING] src.utils.chart_renderer:1909 - 词云图添加数据失败，尝试最基本参数: too many values to unpack (expected 2)
2025-08-05 13:18:40 [WARNING] src.utils.chart_renderer:1909 - 词云图添加数据失败，尝试最基本参数: too many values to unpack (expected 2)
2025-08-05 13:24:41 [WARNING] src.views.main_window:1927 - tabMoved信号不可用，将使用替代方案监控标签页顺序
2025-08-05 13:24:41 [WARNING] src.views.problem_editor_dock:592 - Master field list is empty. Cannot reliably apply group settings.
2025-08-05 13:24:59 [WARNING] src.utils.chart_renderer:1909 - 词云图添加数据失败，尝试最基本参数: too many values to unpack (expected 2)
2025-08-05 13:24:59 [WARNING] src.utils.chart_renderer:1909 - 词云图添加数据失败，尝试最基本参数: too many values to unpack (expected 2)
2025-08-05 13:48:18 [WARNING] src.views.main_window:1927 - tabMoved信号不可用，将使用替代方案监控标签页顺序
2025-08-05 13:48:18 [WARNING] src.views.problem_editor_dock:592 - Master field list is empty. Cannot reliably apply group settings.
2025-08-05 13:48:37 [WARNING] src.utils.chart_renderer:1910 - 词云图添加数据失败，尝试最基本参数: too many values to unpack (expected 2)
2025-08-05 13:48:37 [WARNING] src.utils.chart_renderer:1910 - 词云图添加数据失败，尝试最基本参数: too many values to unpack (expected 2)
2025-08-05 13:58:31 [WARNING] src.views.main_window:1927 - tabMoved信号不可用，将使用替代方案监控标签页顺序
2025-08-05 13:58:31 [WARNING] src.views.problem_editor_dock:592 - Master field list is empty. Cannot reliably apply group settings.
2025-08-05 14:14:44 [WARNING] src.views.main_window:1927 - tabMoved信号不可用，将使用替代方案监控标签页顺序
2025-08-05 14:14:45 [WARNING] src.views.problem_editor_dock:592 - Master field list is empty. Cannot reliably apply group settings.
2025-08-05 14:15:02 [WARNING] src.utils.chart_renderer:1947 - 词云图添加数据失败，尝试最基本参数: too many values to unpack (expected 2)
2025-08-05 14:15:03 [WARNING] src.utils.chart_renderer:1947 - 词云图添加数据失败，尝试最基本参数: too many values to unpack (expected 2)
2025-08-05 14:40:39 [WARNING] src.views.main_window:1990 - tabMoved信号不可用，将使用替代方案监控标签页顺序
2025-08-05 14:40:39 [WARNING] src.views.problem_editor_dock:592 - Master field list is empty. Cannot reliably apply group settings.
2025-08-05 14:40:57 [WARNING] src.utils.chart_renderer:1952 - 词云图添加数据失败，尝试最基本参数: too many values to unpack (expected 2)
2025-08-05 14:40:58 [WARNING] src.utils.chart_renderer:1952 - 词云图添加数据失败，尝试最基本参数: too many values to unpack (expected 2)
2025-08-05 14:48:38 [WARNING] src.views.main_window:1990 - tabMoved信号不可用，将使用替代方案监控标签页顺序
2025-08-05 14:48:38 [WARNING] src.views.problem_editor_dock:592 - Master field list is empty. Cannot reliably apply group settings.
2025-08-05 14:48:55 [WARNING] src.utils.chart_renderer:1952 - 词云图添加数据失败，尝试最基本参数: too many values to unpack (expected 2)
2025-08-05 14:48:55 [WARNING] src.utils.chart_renderer:1952 - 词云图添加数据失败，尝试最基本参数: too many values to unpack (expected 2)
2025-08-05 15:08:05 [WARNING] src.views.main_window:1990 - tabMoved信号不可用，将使用替代方案监控标签页顺序
2025-08-05 15:08:05 [WARNING] src.views.problem_editor_dock:592 - Master field list is empty. Cannot reliably apply group settings.
2025-08-05 15:08:22 [WARNING] src.utils.chart_renderer:2043 - 词云图添加数据失败，尝试最基本参数: too many values to unpack (expected 2)
2025-08-05 15:08:22 [WARNING] src.utils.chart_renderer:2043 - 词云图添加数据失败，尝试最基本参数: too many values to unpack (expected 2)
2025-08-05 15:27:19 [WARNING] src.views.main_window:1990 - tabMoved信号不可用，将使用替代方案监控标签页顺序
2025-08-05 15:27:19 [WARNING] src.views.problem_editor_dock:592 - Master field list is empty. Cannot reliably apply group settings.
2025-08-05 15:27:38 [WARNING] src.utils.chart_renderer:2044 - 词云图添加数据失败，尝试最基本参数: too many values to unpack (expected 2)
2025-08-05 15:27:38 [WARNING] src.utils.chart_renderer:2044 - 词云图添加数据失败，尝试最基本参数: too many values to unpack (expected 2)
2025-08-05 16:09:26 [WARNING] src.views.main_window:1990 - tabMoved信号不可用，将使用替代方案监控标签页顺序
2025-08-05 16:09:26 [WARNING] src.views.problem_editor_dock:592 - Master field list is empty. Cannot reliably apply group settings.
2025-08-05 16:09:43 [ERROR] src.utils.chart_renderer:274 - 🚨 [柱状图] 发现 1 个网络请求:
2025-08-05 16:09:43 [ERROR] src.utils.chart_renderer:277 -    Script src: 1 个
2025-08-05 16:09:43 [ERROR] src.utils.chart_renderer:280 -      1. echarts.min.js
2025-08-05 16:09:43 [ERROR] src.utils.chart_renderer:274 - 🚨 [柱状图] 发现 1 个网络请求:
2025-08-05 16:09:43 [ERROR] src.utils.chart_renderer:277 -    Script src: 1 个
2025-08-05 16:09:43 [ERROR] src.utils.chart_renderer:280 -      1. echarts.min.js
2025-08-05 16:09:43 [ERROR] src.utils.chart_renderer:274 - 🚨 [饼图] 发现 1 个网络请求:
2025-08-05 16:09:43 [ERROR] src.utils.chart_renderer:277 -    Script src: 1 个
2025-08-05 16:09:43 [ERROR] src.utils.chart_renderer:280 -      1. echarts.min.js
2025-08-05 16:09:44 [WARNING] src.utils.chart_renderer:2146 - 词云图添加数据失败，尝试最基本参数: too many values to unpack (expected 2)
2025-08-05 16:09:44 [ERROR] src.utils.chart_renderer:274 - 🚨 [柱状图] 发现 1 个网络请求:
2025-08-05 16:09:44 [ERROR] src.utils.chart_renderer:277 -    Script src: 1 个
2025-08-05 16:09:44 [ERROR] src.utils.chart_renderer:280 -      1. echarts.min.js
2025-08-05 16:09:44 [WARNING] src.utils.chart_renderer:2146 - 词云图添加数据失败，尝试最基本参数: too many values to unpack (expected 2)
2025-08-05 16:09:44 [ERROR] src.utils.chart_renderer:274 - 🚨 [饼图] 发现 1 个网络请求:
2025-08-05 16:09:44 [ERROR] src.utils.chart_renderer:277 -    Script src: 1 个
2025-08-05 16:09:44 [ERROR] src.utils.chart_renderer:280 -      1. echarts.min.js
2025-08-05 16:09:44 [ERROR] src.utils.chart_renderer:274 - 🚨 [饼图] 发现 1 个网络请求:
2025-08-05 16:09:44 [ERROR] src.utils.chart_renderer:277 -    Script src: 1 个
2025-08-05 16:09:44 [ERROR] src.utils.chart_renderer:280 -      1. echarts.min.js
2025-08-05 16:09:44 [ERROR] src.utils.chart_renderer:274 - 🚨 [柱状图] 发现 1 个网络请求:
2025-08-05 16:09:44 [ERROR] src.utils.chart_renderer:277 -    Script src: 1 个
2025-08-05 16:09:44 [ERROR] src.utils.chart_renderer:280 -      1. echarts.min.js
2025-08-05 16:09:45 [ERROR] src.utils.chart_renderer:274 - 🚨 [柱状图] 发现 1 个网络请求:
2025-08-05 16:09:45 [ERROR] src.utils.chart_renderer:277 -    Script src: 1 个
2025-08-05 16:09:45 [ERROR] src.utils.chart_renderer:280 -      1. echarts.min.js
2025-08-05 16:09:45 [ERROR] src.utils.chart_renderer:274 - 🚨 [柱状图] 发现 1 个网络请求:
2025-08-05 16:09:45 [ERROR] src.utils.chart_renderer:277 -    Script src: 1 个
2025-08-05 16:09:45 [ERROR] src.utils.chart_renderer:280 -      1. echarts.min.js
2025-08-05 16:09:45 [ERROR] src.utils.chart_renderer:274 - 🚨 [柱状图] 发现 1 个网络请求:
2025-08-05 16:09:45 [ERROR] src.utils.chart_renderer:277 -    Script src: 1 个
2025-08-05 16:09:45 [ERROR] src.utils.chart_renderer:280 -      1. echarts.min.js
2025-08-05 16:16:05 [WARNING] src.views.main_window:1990 - tabMoved信号不可用，将使用替代方案监控标签页顺序
2025-08-05 16:16:05 [WARNING] src.views.problem_editor_dock:592 - Master field list is empty. Cannot reliably apply group settings.
2025-08-05 16:16:21 [ERROR] src.utils.chart_renderer:274 - 🚨 [柱状图] 发现 1 个网络请求:
2025-08-05 16:16:21 [ERROR] src.utils.chart_renderer:277 -    Script src: 1 个
2025-08-05 16:16:21 [ERROR] src.utils.chart_renderer:280 -      1. echarts.min.js
2025-08-05 16:16:22 [ERROR] src.utils.chart_renderer:274 - 🚨 [柱状图] 发现 1 个网络请求:
2025-08-05 16:16:22 [ERROR] src.utils.chart_renderer:277 -    Script src: 1 个
2025-08-05 16:16:22 [ERROR] src.utils.chart_renderer:280 -      1. echarts.min.js
2025-08-05 16:16:22 [ERROR] src.utils.chart_renderer:274 - 🚨 [饼图] 发现 1 个网络请求:
2025-08-05 16:16:22 [ERROR] src.utils.chart_renderer:277 -    Script src: 1 个
2025-08-05 16:16:22 [ERROR] src.utils.chart_renderer:280 -      1. echarts.min.js
2025-08-05 16:16:22 [WARNING] src.utils.chart_renderer:2146 - 词云图添加数据失败，尝试最基本参数: too many values to unpack (expected 2)
2025-08-05 16:16:22 [ERROR] src.utils.chart_renderer:274 - 🚨 [柱状图] 发现 1 个网络请求:
2025-08-05 16:16:22 [ERROR] src.utils.chart_renderer:277 -    Script src: 1 个
2025-08-05 16:16:22 [ERROR] src.utils.chart_renderer:280 -      1. echarts.min.js
2025-08-05 16:16:22 [WARNING] src.utils.chart_renderer:2146 - 词云图添加数据失败，尝试最基本参数: too many values to unpack (expected 2)
2025-08-05 16:16:22 [ERROR] src.utils.chart_renderer:274 - 🚨 [饼图] 发现 1 个网络请求:
2025-08-05 16:16:22 [ERROR] src.utils.chart_renderer:277 -    Script src: 1 个
2025-08-05 16:16:22 [ERROR] src.utils.chart_renderer:280 -      1. echarts.min.js
2025-08-05 16:16:22 [ERROR] src.utils.chart_renderer:274 - 🚨 [饼图] 发现 1 个网络请求:
2025-08-05 16:16:22 [ERROR] src.utils.chart_renderer:277 -    Script src: 1 个
2025-08-05 16:16:22 [ERROR] src.utils.chart_renderer:280 -      1. echarts.min.js
2025-08-05 16:16:23 [ERROR] src.utils.chart_renderer:274 - 🚨 [柱状图] 发现 1 个网络请求:
2025-08-05 16:16:23 [ERROR] src.utils.chart_renderer:277 -    Script src: 1 个
2025-08-05 16:16:23 [ERROR] src.utils.chart_renderer:280 -      1. echarts.min.js
2025-08-05 16:16:23 [ERROR] src.utils.chart_renderer:274 - 🚨 [柱状图] 发现 1 个网络请求:
2025-08-05 16:16:23 [ERROR] src.utils.chart_renderer:277 -    Script src: 1 个
2025-08-05 16:16:23 [ERROR] src.utils.chart_renderer:280 -      1. echarts.min.js
2025-08-05 16:16:23 [ERROR] src.utils.chart_renderer:274 - 🚨 [柱状图] 发现 1 个网络请求:
2025-08-05 16:16:23 [ERROR] src.utils.chart_renderer:277 -    Script src: 1 个
2025-08-05 16:16:23 [ERROR] src.utils.chart_renderer:280 -      1. echarts.min.js
2025-08-05 16:16:23 [ERROR] src.utils.chart_renderer:274 - 🚨 [柱状图] 发现 1 个网络请求:
2025-08-05 16:16:23 [ERROR] src.utils.chart_renderer:277 -    Script src: 1 个
2025-08-05 16:16:23 [ERROR] src.utils.chart_renderer:280 -      1. echarts.min.js
2025-08-05 16:17:24 [ERROR] src.views.chart_creator:2450 - 📊 ❌ Y轴字段 'count_value' 不存在于数据中!
2025-08-05 16:17:24 [ERROR] src.views.chart_creator:2450 - 📊 ❌ Y轴字段 'count_value' 不存在于数据中!
2025-08-05 16:17:28 [ERROR] src.views.chart_creator:2450 - 📊 ❌ Y轴字段 'count_value' 不存在于数据中!
2025-08-05 16:17:28 [ERROR] src.utils.chart_renderer:274 - 🚨 [柱状图] 发现 1 个网络请求:
2025-08-05 16:17:28 [ERROR] src.utils.chart_renderer:277 -    Script src: 1 个
2025-08-05 16:17:28 [ERROR] src.utils.chart_renderer:280 -      1. echarts.min.js
2025-08-05 16:17:30 [ERROR] src.views.chart_creator:2450 - 📊 ❌ Y轴字段 'count_value' 不存在于数据中!
2025-08-05 16:17:30 [ERROR] src.utils.chart_renderer:274 - 🚨 [柱状图] 发现 1 个网络请求:
2025-08-05 16:17:30 [ERROR] src.utils.chart_renderer:277 -    Script src: 1 个
2025-08-05 16:17:30 [ERROR] src.utils.chart_renderer:280 -      1. echarts.min.js
2025-08-05 16:17:38 [ERROR] src.utils.chart_renderer:274 - 🚨 [柱状图] 发现 1 个网络请求:
2025-08-05 16:17:38 [ERROR] src.utils.chart_renderer:277 -    Script src: 1 个
2025-08-05 16:17:38 [ERROR] src.utils.chart_renderer:280 -      1. echarts.min.js
2025-08-05 16:17:42 [ERROR] src.utils.chart_renderer:274 - 🚨 [柱状图] 发现 1 个网络请求:
2025-08-05 16:17:42 [ERROR] src.utils.chart_renderer:277 -    Script src: 1 个
2025-08-05 16:17:42 [ERROR] src.utils.chart_renderer:280 -      1. echarts.min.js
2025-08-05 16:17:44 [ERROR] src.utils.chart_renderer:274 - 🚨 [柱状图] 发现 1 个网络请求:
2025-08-05 16:17:44 [ERROR] src.utils.chart_renderer:277 -    Script src: 1 个
2025-08-05 16:17:44 [ERROR] src.utils.chart_renderer:280 -      1. echarts.min.js
2025-08-05 16:17:50 [ERROR] src.utils.chart_renderer:274 - 🚨 [柱状图] 发现 1 个网络请求:
2025-08-05 16:17:50 [ERROR] src.utils.chart_renderer:277 -    Script src: 1 个
2025-08-05 16:17:50 [ERROR] src.utils.chart_renderer:280 -      1. echarts.min.js
2025-08-05 16:17:50 [ERROR] src.views.dashboard_viewer:437 - 🚨 [看板:试验进度及费用] 发现 4 个网络请求:
2025-08-05 16:17:50 [ERROR] src.views.dashboard_viewer:439 -    HTTP/HTTPS URLs: 4 个
2025-08-05 16:17:50 [ERROR] src.views.dashboard_viewer:441 -      1. http://www.apache.org/licenses/LICENSE-2.0
2025-08-05 16:17:50 [ERROR] src.views.dashboard_viewer:441 -      2. https://github.com/ecomfe/zrender/blob/master/LICENSE.txt
2025-08-05 16:17:50 [ERROR] src.views.dashboard_viewer:441 -      3. http://www.w3.org/2000/svg&quot;,tw=&quot;http://www.w3.org/1999/xlink&quot;;function
2025-08-05 16:17:50 [ERROR] src.views.dashboard_viewer:443 -      ... 还有 1 个
2025-08-05 16:18:00 [ERROR] src.utils.chart_renderer:274 - 🚨 [柱状图] 发现 1 个网络请求:
2025-08-05 16:18:00 [ERROR] src.utils.chart_renderer:277 -    Script src: 1 个
2025-08-05 16:18:00 [ERROR] src.utils.chart_renderer:280 -      1. echarts.min.js
2025-08-05 16:18:07 [ERROR] src.utils.chart_renderer:274 - 🚨 [柱状图] 发现 1 个网络请求:
2025-08-05 16:18:07 [ERROR] src.utils.chart_renderer:277 -    Script src: 1 个
2025-08-05 16:18:07 [ERROR] src.utils.chart_renderer:280 -      1. echarts.min.js
2025-08-05 16:18:12 [ERROR] src.utils.chart_renderer:274 - 🚨 [柱状图] 发现 1 个网络请求:
2025-08-05 16:18:12 [ERROR] src.utils.chart_renderer:277 -    Script src: 1 个
2025-08-05 16:18:12 [ERROR] src.utils.chart_renderer:280 -      1. echarts.min.js
2025-08-05 16:18:16 [ERROR] src.utils.chart_renderer:274 - 🚨 [柱状图] 发现 1 个网络请求:
2025-08-05 16:18:16 [ERROR] src.utils.chart_renderer:277 -    Script src: 1 个
2025-08-05 16:18:16 [ERROR] src.utils.chart_renderer:280 -      1. echarts.min.js
2025-08-05 16:18:16 [ERROR] src.views.dashboard_viewer:437 - 🚨 [看板:试验进度及费用] 发现 4 个网络请求:
2025-08-05 16:18:16 [ERROR] src.views.dashboard_viewer:439 -    HTTP/HTTPS URLs: 4 个
2025-08-05 16:18:16 [ERROR] src.views.dashboard_viewer:441 -      1. http://www.apache.org/licenses/LICENSE-2.0
2025-08-05 16:18:16 [ERROR] src.views.dashboard_viewer:441 -      2. https://github.com/ecomfe/zrender/blob/master/LICENSE.txt
2025-08-05 16:18:16 [ERROR] src.views.dashboard_viewer:441 -      3. http://www.w3.org/2000/svg&quot;,tw=&quot;http://www.w3.org/1999/xlink&quot;;function
2025-08-05 16:18:16 [ERROR] src.views.dashboard_viewer:443 -      ... 还有 1 个
2025-08-05 16:18:24 [ERROR] src.utils.chart_renderer:274 - 🚨 [柱状图] 发现 1 个网络请求:
2025-08-05 16:18:24 [ERROR] src.utils.chart_renderer:277 -    Script src: 1 个
2025-08-05 16:18:24 [ERROR] src.utils.chart_renderer:280 -      1. echarts.min.js
