<!DOCTYPE html>
<html>
<head>
<style>
    .chart-responsive {
        width: 100% !important;
        height: 100% !important;
        max-width: 500px;
        max-height: 350px;
        margin: 0 auto;
        display: block;
    }
    
    /* 确保ECharts容器正确显示 */
    div[_echarts_instance_] {
        width: 500px !important;
        height: 350px !important;
    }
    
    /* 防止图表被压缩 */
    canvas {
        max-width: none !important;
        max-height: none !important;
    }
</style>

    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="echarts.min.js"></script>

    
</head>
<body >
    <div id="chart_2713758220672" class="chart-container" style="  ; width: 500px; height: 350px;"></div>
    <script>
        var chart_9846bebb85f3448d8ea96d528e4b6655 = echarts.init(
            document.getElementById('chart_2713758220672'), 'white', {renderer: 'canvas'});
        var option_9846bebb85f3448d8ea96d528e4b6655 = {
    "animation": false,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470C6",
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "bar",
            "name": "value",
            "legendHoverLink": true,
            "data": [
                0,
                0,
                0
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": false,
                "margin": 8,
                "fontSize": 6,
                "valueAnimation": false
            }
        }
    ],
    "legend": [
        {
            "data": [
                "value"
            ],
            "selected": {},
            "show": true,
            "left": "center",
            "top": "5%",
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "textStyle": {
                "fontSize": 12
            },
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": false,
        "trigger": "none",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "name": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "nameTextStyle": {
                "fontWeight": "normal",
                "fontSize": 12
            },
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "margin": 8,
                "fontSize": 10,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "\u96c6\u6210\u6d4b\u8bd5A",
                "\u96c6\u6210\u6d4b\u8bd5B",
                "\u96c6\u6210\u6d4b\u8bd5C"
            ]
        }
    ],
    "yAxis": [
        {
            "name": "value",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "nameTextStyle": {
                "fontWeight": "normal",
                "fontSize": 12
            },
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "margin": 8,
                "fontSize": 10,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "\u96c6\u6210\u6d4b\u8bd5\u56fe\u8868",
            "target": "blank",
            "subtarget": "blank",
            "left": "center",
            "top": "5%",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false,
            "textStyle": {
                "fontWeight": "bold",
                "fontSize": 14
            }
        }
    ],
    "dataZoom": [
        {
            "show": false,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        }
    ],
    "grid": [
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "15%",
            "right": "5%",
            "bottom": "10%",
            "containLabel": true,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        }
    ]
};
        chart_9846bebb85f3448d8ea96d528e4b6655.setOption(option_9846bebb85f3448d8ea96d528e4b6655);
    </script>
<script type="text/javascript">

        // 图表交互代码 - category_柱状图
        (function() {
            // 等待图表对象就绪
            var waitForChart = function() {
                var chartInstance = null;
                
                // 方法1：查找页面中的所有图表容器
                var chartDivs = document.querySelectorAll('div[id*="chart"], div[_echarts_instance_]');
                for (var i = 0; i < chartDivs.length; i++) {
                    if (typeof echarts !== 'undefined' && echarts.getInstanceByDom) {
                        var instance = echarts.getInstanceByDom(chartDivs[i]);
                        if (instance) {
                            chartInstance = instance;
                            break;
                        }
                    }
                }
                
                if (chartInstance) {
                    console.log('图表对象就绪: category_柱状图');
                    setupChartInteraction(chartInstance);
                } else {
                    setTimeout(waitForChart, 100);
                }
            };
            
            var setupChartInteraction = function(chartInstance) {
                try {
                    // 添加点击事件监听
                    chartInstance.on('click', function(params) {
                        console.log('图表点击事件:', params);
                        
                        // 构建点击数据
                        var clickData = {
                            chartId: 'category_柱状图',
                            name: params.name,
                            value: params.value,
                            data: params.data,
                            seriesName: params.seriesName,
                            dataIndex: params.dataIndex,
                            seriesIndex: params.seriesIndex
                        };
                        
                        // 保存到window对象
                        window.lastChartClickData = clickData;
                        console.log('图表点击数据已保存:', clickData);
                    });
                    
                    console.log('图表交互设置完成: category_柱状图');
                } catch (e) {
                    console.error('设置图表交互失败:', e);
                }
            };
            
            // 开始等待图表就绪
            waitForChart();
        })();
        

            // 图表交互代码 - chart_2713758220672
            (function() {
                console.log('🚀 [图表交互] 初始化图表交互: chart_2713758220672');

                // 等待图表对象就绪
                var waitForChart = function() {
                    var chartInstance = null;
                    console.log('🔍 [图表交互] 等待图表就绪: chart_2713758220672');

                    // 查找图表实例
                    if (typeof echarts !== 'undefined') {
                        console.log('✅ [图表交互] ECharts已加载');

                        // 🔧 方法1：通过特定的图表ID查找对应的DOM元素
                        var targetChartDiv = document.getElementById('chart_2713758220672');
                        if (targetChartDiv) {
                            chartInstance = echarts.getInstanceByDom(targetChartDiv);
                            if (chartInstance) {
                                console.log('✅ [图表交互] 通过ID找到目标图表实例: chart_2713758220672');
                            }
                        }

                        // 🔧 方法2：如果没有找到，尝试通过包含图表ID的div查找
                        if (!chartInstance) {
                            var chartDivs = document.querySelectorAll('div[id*="chart_2713758220672"], div[_echarts_instance_]');
                            console.log('🔍 [图表交互] 通过ID模糊匹配发现图表容器数量:', chartDivs.length);

                            for (var i = 0; i < chartDivs.length; i++) {
                                var instance = echarts.getInstanceByDom(chartDivs[i]);
                                if (instance) {
                                    console.log('✅ [图表交互] 找到匹配的图表实例:', i, chartDivs[i].id, instance);
                                    chartInstance = instance;
                                    break;
                                }
                            }
                        }

                        // 🔧 方法3：通过全局变量查找
                        if (!chartInstance && window.chart_chart_2713758220672) {
                            console.log('✅ [图表交互] 通过全局变量找到图表实例');
                            chartInstance = window.chart_chart_2713758220672;
                        }

                        // 🔧 方法4：最后的备用方案，查找最新创建的图表实例
                        if (!chartInstance) {
                            var allChartDivs = document.querySelectorAll('div[_echarts_instance_]');
                            console.log('🔍 [图表交互] 备用方案：发现所有图表容器数量:', allChartDivs.length);

                            // 查找最后一个图表实例（通常是最新创建的）
                            for (var i = allChartDivs.length - 1; i >= 0; i--) {
                                var instance = echarts.getInstanceByDom(allChartDivs[i]);
                                if (instance) {
                                    console.log('⚠️ [图表交互] 使用备用方案找到图表实例:', i, allChartDivs[i].id, instance);
                                    chartInstance = instance;
                                    break;
                                }
                            }
                        }
                    } else {
                        console.log('⚠️ [图表交互] ECharts未加载，继续等待...');
                    }

                    if (chartInstance) {
                        console.log('🎉 [图表交互] 图表对象就绪: chart_2713758220672');
                        setupChartInteraction(chartInstance);
                    } else {
                        console.log('⏳ [图表交互] 图表未就绪，100ms后重试: chart_2713758220672');
                        setTimeout(waitForChart, 100);
                    }
                };

                var setupChartInteraction = function(chartInstance) {
                    try {
                        console.log('🔧 [图表交互] 开始设置图表点击监听: chart_2713758220672');

                        // 添加点击事件监听
                        chartInstance.on('click', function(params) {
                            console.log('🎯 [图表交互] 图表点击事件触发!', params);
                            console.log('🎯 [图表交互] 点击的图表ID: chart_2713758220672');
                            console.log('🎯 [图表交互] 点击的元素名称:', params.name);
                            console.log('🎯 [图表交互] 点击的数值:', params.value);

                            // 构建点击数据
                            var clickData = {
                                chartId: 'chart_2713758220672',
                                chartType: '柱状图',
                                targetTable: '试验问题表',
                                name: params.name,
                                value: params.value,
                                data: params.data,
                                seriesName: params.seriesName,
                                dataIndex: params.dataIndex,
                                seriesIndex: params.seriesIndex,
                                fieldMapping: {"name": "\u7c7b\u522b"}
                            };

                            // 构建筛选条件
                            var filterConditions = {};
                            var fieldMapping = clickData.fieldMapping;
                            console.log('🔧 [图表交互] 字段映射配置:', fieldMapping);

                            // 根据字段映射构建筛选条件
                            for (var chartField in fieldMapping) {
                                var tableField = fieldMapping[chartField];
                                var value = null;

                                if (chartField === 'name' && params.name) {
                                    value = params.name;
                                } else if (chartField === 'value' && params.value !== undefined) {
                                    value = params.value;
                                } else if (chartField === 'seriesName' && params.seriesName) {
                                    value = params.seriesName;
                                } else if (params.data && typeof params.data === 'object' && params.data[chartField]) {
                                    value = params.data[chartField];
                                }

                                if (value !== null && value !== undefined) {
                                    filterConditions[tableField] = value;
                                    console.log('🔧 [图表交互] 添加筛选条件:', tableField, '=', value);
                                }
                            }

                            clickData.filterConditions = filterConditions;
                            console.log('📊 [图表交互] 完整点击数据:', clickData);

                            // 保存到window对象供Python端读取
                            window.lastChartClickData = clickData;
                            console.log('✅ [图表交互] 点击数据已保存到window.lastChartClickData');

                            // 同时保存到localStorage作为备用方案
                            try {
                                // 检查localStorage是否可用
                                if (typeof Storage !== 'undefined' && typeof localStorage !== 'undefined') {
                                    localStorage.setItem('chart_click_data', JSON.stringify(clickData));
                                    console.log('✅ [图表交互] 点击数据已保存到localStorage');
                                }
                            } catch (e) {
                                console.log('⚠️ [图表交互] localStorage访问失败:', e);
                            }

                            // 尝试通过Qt桥接发送数据
                            if (typeof qtBridge !== 'undefined' && qtBridge.handleChartClick) {
                                console.log('🔗 [图表交互] 使用qtBridge发送数据');
                                qtBridge.handleChartClick(JSON.stringify(clickData));
                            } else if (typeof chartNavigationBridge !== 'undefined' && chartNavigationBridge.handleChartClick) {
                                console.log('🔗 [图表交互] 使用chartNavigationBridge发送数据');
                                // 使用新的导航桥接
                                var navigationData = {
                                    chartId: 'chart_2713758220672',
                                    chartType: '柱状图',
                                    clickedElement: params.name || '',
                                    clickedValue: params.value || '',
                                    seriesName: params.seriesName || '',
                                    dataIndex: params.dataIndex || 0,
                                    rawData: {
                                        data: params.data,
                                        color: params.color,
                                        seriesIndex: params.seriesIndex,
                                        componentType: params.componentType
                                    }
                                };
                                chartNavigationBridge.handleChartClick(JSON.stringify(navigationData));
                            } else {
                                console.log('⚠️ [图表交互] 没有可用的Qt桥接，依赖Python轮询检测');
                            }

                            // 显示用户反馈
                            if (filterConditions && Object.keys(filterConditions).length > 0) {
                                console.log('🚀 [图表交互] 将跳转到表格:', clickData.targetTable, '筛选条件:', filterConditions);
                                // 🔧 添加视觉反馈
                                if (params.event && params.event.target) {
                                    var originalColor = params.event.target.style.opacity;
                                    params.event.target.style.opacity = '0.7';
                                    setTimeout(function() {
                                        params.event.target.style.opacity = originalColor;
                                    }, 200);
                                }
                            } else {
                                console.log('⚠️ [图表交互] 没有有效的筛选条件');
                            }
                        });

                        console.log('✅ [图表交互] 图表交互设置完成: chart_2713758220672');
                    } catch (e) {
                        console.error('❌ [图表交互] 设置图表交互失败:', e);
                    }
                };

                // 开始等待图表就绪
                setTimeout(waitForChart, 100);
            })();
            
</script>
</body>
</html>
