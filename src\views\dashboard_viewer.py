#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import logging
import json
import tempfile
import html as html_parser
from typing import Dict, List, Any, Optional

from PyQt5.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QToolBar, QAction,
    QComboBox, QLabel, QPushButton, QMessageBox, QFrame, QSplitter,
    QListWidget, QListWidgetItem, QFileDialog, QProgressBar, QStatusBar
)
from PyQt5.QtCore import Qt, pyqtSignal, QUrl, QThread, QTimer
from PyQt5.QtGui import QIcon, QFont

# 尝试导入QWebEngineView，如果失败则使用替代方案
try:
    from PyQt5.QtWebEngineWidgets import QWebEngineView
    WEB_ENGINE_AVAILABLE = True
except ImportError:
    WEB_ENGINE_AVAILABLE = False
    # 使用QTextEdit作为替代
    from PyQt5.QtWidgets import QTextEdit as QWebEngineView

from src.models.visualization import VisualizationModel

class ChartRenderer(QThread):
    """图表渲染线程"""
    
    chart_rendered = pyqtSignal(str, str)  # 图表ID, HTML内容
    error_occurred = pyqtSignal(str, str)  # 图表ID, 错误信息
    
    def __init__(self, chart_config: Dict[str, Any]):
        super().__init__()
        self.chart_config = chart_config
    
    def run(self):
        """执行图表渲染"""
        try:
            # 这里将使用PyEcharts生成图表HTML
            # 目前先返回占位符HTML
            chart_id = str(self.chart_config.get('id', 'unknown'))
            chart_name = self.chart_config.get('name', '未知图表')
            
            html_content = f"""
            <div style="width: 100%; height: 100%; display: flex; align-items: center; justify-content: center; 
                        border: 2px dashed #ccc; background-color: #f9f9f9;">
                <div style="text-align: center;">
                    <h3>{chart_name}</h3>
                    <p>图表类型: {self.chart_config.get('chart_type', '未知')}</p>
                    <p>PyEcharts渲染功能正在开发中...</p>
                </div>
            </div>
            """
            
            self.chart_rendered.emit(chart_id, html_content)
            
        except Exception as e:
            chart_id = str(self.chart_config.get('id', 'unknown'))
            self.error_occurred.emit(chart_id, str(e))

class DashboardViewer(QMainWindow):
    """看板查看器"""

    # 🔧 新增：定义信号，用于与主界面通信
    dashboard_add_to_main_requested = pyqtSignal(dict)  # 请求添加看板到主界面
    dashboard_remove_from_main_requested = pyqtSignal(dict)  # 请求从主界面移除看板

    def __init__(self, db_path: str, parent=None):
        """
        初始化看板查看器
        
        参数:
            db_path: 数据库路径
            parent: 父窗口
        """
        super().__init__(parent)
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)
        
        # 初始化数据模型
        self.visualization_model = VisualizationModel(db_path)
        
        # 当前看板数据
        self.current_dashboard = None
        self.dashboard_html = ""
        self.chart_renderers = {}

        # 🔧 新增：跟踪看板在主界面的状态
        self._dashboard_main_status = {}  # {dashboard_name: bool}
        
        # 初始化UI
        self.init_ui()
        
        # 连接信号
        self.connect_signals()
        
        # 加载数据
        self.load_initial_data()

        # 🔧 新增: 从数据库加载看板的主界面状态
        self.load_all_dashboard_main_statuses()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("看板查看器")
        self.setWindowIcon(QIcon('src/resources/icons/数据分析.png'))
        self.resize(1400, 900)
        
        # 创建工具栏
        self.create_toolbar()
        
        # 创建中央部件
        self.create_central_widget()
        
        # 创建状态栏
        self.create_status_bar()
    
    def create_toolbar(self):
        """创建工具栏"""
        self.toolbar = QToolBar()
        self.toolbar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)
        self.addToolBar(self.toolbar)

        # 看板选择
        self.toolbar.addWidget(QLabel("选择看板:"))
        self.dashboard_combo = QComboBox()
        self.dashboard_combo.setMinimumWidth(200)
        self.toolbar.addWidget(self.dashboard_combo)

        self.toolbar.addSeparator()

        # 🔧 新增：添加到主界面的按钮
        self.add_to_main_action = QAction("添加到主界面", self)
        self.add_to_main_action.setToolTip("将当前选中的看板添加到主界面显示")
        self.add_to_main_action.setEnabled(False)  # 初始状态禁用
        self.toolbar.addAction(self.add_to_main_action)

        self.remove_from_main_action = QAction("取消添加到主界面", self)
        self.remove_from_main_action.setToolTip("从主界面移除当前选中的看板")
        self.remove_from_main_action.setEnabled(False)  # 初始状态禁用
        self.toolbar.addAction(self.remove_from_main_action)

        self.toolbar.addSeparator()

        # 刷新按钮
        self.refresh_action = QAction(QIcon('src/resources/icons/数据分析.png'), "刷新", self)
        self.toolbar.addAction(self.refresh_action)

        # 全屏按钮
        self.fullscreen_action = QAction("全屏", self)
        self.toolbar.addAction(self.fullscreen_action)

        self.toolbar.addSeparator()

        # 导出按钮
        self.export_html_action = QAction("导出HTML", self)
        self.export_pdf_action = QAction("导出PDF", self)
        self.toolbar.addAction(self.export_html_action)
        self.toolbar.addAction(self.export_pdf_action)

        self.toolbar.addSeparator()

        # 设置按钮
        self.settings_action = QAction("设置", self)
        self.toolbar.addAction(self.settings_action)
    
    def create_central_widget(self):
        """创建中央部件"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)
        
        # 创建分割器
        self.splitter = QSplitter(Qt.Horizontal)
        layout.addWidget(self.splitter)
        
        # 左侧看板列表
        self.create_dashboard_list()
        self.splitter.addWidget(self.dashboard_list_frame)
        
        # 右侧查看区域
        self.create_viewer_area()
        self.splitter.addWidget(self.viewer_frame)
        
        # 设置分割器比例
        self.splitter.setSizes([250, 1150])
    
    def create_dashboard_list(self):
        """创建看板列表"""
        self.dashboard_list_frame = QFrame()
        self.dashboard_list_frame.setFrameStyle(QFrame.StyledPanel)
        self.dashboard_list_frame.setMaximumWidth(300)
        
        layout = QVBoxLayout(self.dashboard_list_frame)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # 标题
        title_label = QLabel("看板列表")
        title_label.setFont(QFont("Arial", 12, QFont.Bold))
        layout.addWidget(title_label)
        
        # 看板列表
        self.dashboard_list = QListWidget()
        layout.addWidget(self.dashboard_list)
        
        # 操作按钮
        button_layout = QHBoxLayout()
        
        self.load_dashboard_btn = QPushButton("加载看板")
        self.delete_dashboard_btn = QPushButton("删除看板")
        
        button_layout.addWidget(self.load_dashboard_btn)
        button_layout.addWidget(self.delete_dashboard_btn)
        
        layout.addLayout(button_layout)
    
    def create_viewer_area(self):
        """创建查看区域"""
        self.viewer_frame = QFrame()
        self.viewer_frame.setFrameStyle(QFrame.StyledPanel)
        
        layout = QVBoxLayout(self.viewer_frame)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # 🔧 修复：隐藏看板标题，最大化显示空间
        self.dashboard_title = QLabel("请选择要查看的看板")
        self.dashboard_title.setFont(QFont("Arial", 16, QFont.Bold))
        self.dashboard_title.setAlignment(Qt.AlignCenter)
        self.dashboard_title.setVisible(False)  # 隐藏标题栏
        # layout.addWidget(self.dashboard_title)  # 不添加到布局中
        
        # Web视图或替代视图
        if WEB_ENGINE_AVAILABLE:
            self.web_view = QWebEngineView()

            # 🔧 关键修复：强制设置WebEngine离线模式
            self._configure_webengine_offline()

            self.web_view.setHtml(self.get_default_html())
        else:
            self.web_view = QWebEngineView()  # 实际上是QTextEdit
            self.web_view.setHtml(self.get_default_html())
            self.web_view.setReadOnly(True)

        # 🔧 修复：确保WebView没有额外的边距和样式
        self.web_view.setContentsMargins(0, 0, 0, 0)
        self.web_view.setStyleSheet("QWebEngineView { border: none; margin: 0; padding: 0; }")

        layout.addWidget(self.web_view)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)

    def _configure_webengine_offline(self):
        """🔧 关键修复：配置WebEngine完全离线模式"""
        try:
            if WEB_ENGINE_AVAILABLE:
                from PyQt5.QtWebEngineWidgets import QWebEngineSettings

                # 获取页面设置
                page = self.web_view.page()
                settings = page.settings()

                # 应用最强力的离线设置
                offline_settings = [
                    (QWebEngineSettings.LocalContentCanAccessRemoteUrls, False),
                    (QWebEngineSettings.XSSAuditingEnabled, False),
                    (QWebEngineSettings.JavascriptCanOpenWindows, False),
                    (QWebEngineSettings.JavascriptCanAccessClipboard, False),
                    (QWebEngineSettings.LocalContentCanAccessFileUrls, True),
                    (QWebEngineSettings.AllowRunningInsecureContent, True),
                    (QWebEngineSettings.PluginsEnabled, False),
                    (QWebEngineSettings.Accelerated2dCanvasEnabled, False),
                    (QWebEngineSettings.WebGLEnabled, False),
                    (QWebEngineSettings.HyperlinkAuditingEnabled, False),
                    (QWebEngineSettings.ErrorPageEnabled, False),
                    (QWebEngineSettings.FocusOnNavigationEnabled, False),
                    (QWebEngineSettings.PrintElementBackgrounds, False),
                    (QWebEngineSettings.AllowWindowActivationFromJavaScript, False),
                    (QWebEngineSettings.ShowScrollBars, True),
                ]

                for setting, value in offline_settings:
                    try:
                        settings.setAttribute(setting, value)
                    except Exception as e:
                        self.logger.warning(f"⚠️ 设置WebEngine属性失败: {setting.name} - {e}")

                self.logger.info("✅ 看板查看器WebEngine离线设置已应用")

        except Exception as e:
            self.logger.warning(f"⚠️ 看板查看器WebEngine离线配置失败: {e}")

    def _clean_html_for_offline(self, html_content):
        """🔧 关键修复：清理HTML中的所有网络请求，确保完全离线"""
        if not html_content:
            return html_content

        try:
            import re

            # 记录原始长度
            original_length = len(html_content)

            # 1. 移除所有HTTP/HTTPS/file://链接
            html_content = re.sub(r'https?://[^\s"\'<>]+', '', html_content)
            html_content = re.sub(r'file://[^\s"\'<>]+', '', html_content)
            html_content = re.sub(r'ftp://[^\s"\'<>]+', '', html_content)

            # 2. 移除CDN相关的script标签
            html_content = re.sub(r'<script[^>]*src=["\'][^"\']*cdn[^"\']*["\'][^>]*></script>', '', html_content, flags=re.IGNORECASE)
            html_content = re.sub(r'<script[^>]*src=["\'][^"\']*assets\.pyecharts[^"\']*["\'][^>]*></script>', '', html_content, flags=re.IGNORECASE)

            # 3. 移除外部资源加载
            html_content = re.sub(r'<link[^>]*href=["\'][^"\']*["\'][^>]*>', '', html_content, flags=re.IGNORECASE)

            # 4. 移除可能的网络请求相关代码
            network_patterns = [
                r'fetch\s*\([^)]*\)',
                r'XMLHttpRequest\s*\([^)]*\)',
                r'\.load\s*\(["\'][^"\']*http[^"\']*["\']',
                r'import\s+[^;]*from\s+["\'][^"\']*http[^"\']*["\']',
            ]

            for pattern in network_patterns:
                html_content = re.sub(pattern, '', html_content, flags=re.IGNORECASE)

            # 5. 清理JavaScript注释中的URL
            def clean_js_comment(match):
                comment = match.group(0)
                comment = re.sub(r'https?://[^\s]*', '', comment)
                return comment

            html_content = re.sub(r'/\*.*?\*/', clean_js_comment, html_content, flags=re.DOTALL)
            html_content = re.sub(r'//.*?$', clean_js_comment, html_content, flags=re.MULTILINE)

            # 记录清理结果
            cleaned_length = len(html_content)
            if cleaned_length != original_length:
                self.logger.info(f"✅ 看板HTML网络请求已清理：{original_length} -> {cleaned_length} 字符")

            return html_content

        except Exception as e:
            self.logger.warning(f"⚠️ 看板HTML网络请求清理失败: {e}")
            return html_content

    def _log_network_requests_in_html(self, html_content, dashboard_name):
        """🔍 详细记录看板HTML中的网络请求，用于调试"""
        if not html_content:
            return

        try:
            import re

            # 检查各种网络请求模式
            patterns = {
                'HTTP/HTTPS URLs': r'https?://[^\s"\'<>]+',
                'File URLs': r'file://[^\s"\'<>]+',
                'FTP URLs': r'ftp://[^\s"\'<>]+',
                'Script src': r'<script[^>]*src=["\']([^"\']*)["\'][^>]*>',
                'Link href': r'<link[^>]*href=["\']([^"\']*)["\'][^>]*>',
                'Fetch calls': r'fetch\s*\(["\']([^"\']*)["\']',
                'XMLHttpRequest': r'XMLHttpRequest.*?open\s*\([^,]*,\s*["\']([^"\']*)["\']',
                'Import from URL': r'import\s+[^;]*from\s+["\']([^"\']*http[^"\']*)["\']',
            }

            found_requests = {}
            total_requests = 0

            for pattern_name, pattern in patterns.items():
                matches = re.findall(pattern, html_content, re.IGNORECASE)
                if matches:
                    found_requests[pattern_name] = matches
                    total_requests += len(matches)

            if total_requests > 0:
                print(f"🚨 [看板:{dashboard_name}] 发现 {total_requests} 个网络请求:")
                self.logger.error(f"🚨 [看板:{dashboard_name}] 发现 {total_requests} 个网络请求:")
                for pattern_name, matches in found_requests.items():
                    print(f"   {pattern_name}: {len(matches)} 个")
                    self.logger.error(f"   {pattern_name}: {len(matches)} 个")
                    for i, match in enumerate(matches[:3]):  # 只显示前3个
                        print(f"     {i+1}. {match}")
                        self.logger.error(f"     {i+1}. {match}")
                    if len(matches) > 3:
                        print(f"     ... 还有 {len(matches) - 3} 个")
                        self.logger.error(f"     ... 还有 {len(matches) - 3} 个")
            else:
                print(f"✅ [看板:{dashboard_name}] 未发现网络请求")
                self.logger.error(f"✅ [看板:{dashboard_name}] 未发现网络请求")

        except Exception as e:
            print(f"⚠️ 看板网络请求日志记录失败: {e}")
            self.logger.warning(f"⚠️ 看板网络请求日志记录失败: {e}")

    def _log_network_requests_in_html(self, html_content, dashboard_name):
        """🔍 详细记录看板HTML中的网络请求，用于调试"""
        if not html_content:
            return

        try:
            import re

            # 检查各种网络请求模式
            patterns = {
                'HTTP/HTTPS URLs': r'https?://[^\s"\'<>]+',
                'File URLs': r'file://[^\s"\'<>]+',
                'FTP URLs': r'ftp://[^\s"\'<>]+',
                'Script src': r'<script[^>]*src=["\']([^"\']*)["\'][^>]*>',
                'Link href': r'<link[^>]*href=["\']([^"\']*)["\'][^>]*>',
                'Fetch calls': r'fetch\s*\(["\']([^"\']*)["\']',
                'XMLHttpRequest': r'XMLHttpRequest.*?open\s*\([^,]*,\s*["\']([^"\']*)["\']',
                'Import from URL': r'import\s+[^;]*from\s+["\']([^"\']*http[^"\']*)["\']',
            }

            found_requests = {}
            total_requests = 0

            for pattern_name, pattern in patterns.items():
                matches = re.findall(pattern, html_content, re.IGNORECASE)
                if matches:
                    found_requests[pattern_name] = matches
                    total_requests += len(matches)

            if total_requests > 0:
                self.logger.error(f"🚨 [看板:{dashboard_name}] 发现 {total_requests} 个网络请求:")
                for pattern_name, matches in found_requests.items():
                    self.logger.error(f"   {pattern_name}: {len(matches)} 个")
                    for i, match in enumerate(matches[:3]):  # 只显示前3个
                        self.logger.error(f"     {i+1}. {match}")
                    if len(matches) > 3:
                        self.logger.error(f"     ... 还有 {len(matches) - 3} 个")
            else:
                self.logger.info(f"✅ [看板:{dashboard_name}] 未发现网络请求")

        except Exception as e:
            self.logger.warning(f"⚠️ 看板网络请求日志记录失败: {e}")

    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("看板查看器已就绪")
    
    def connect_signals(self):
        """连接信号和槽"""
        # 工具栏信号
        self.dashboard_combo.currentTextChanged.connect(self.on_dashboard_selected)
        self.add_to_main_action.triggered.connect(self.add_dashboard_to_main)
        self.remove_from_main_action.triggered.connect(self.remove_dashboard_from_main)
        self.refresh_action.triggered.connect(self.refresh_data)
        self.fullscreen_action.triggered.connect(self.toggle_fullscreen)
        self.export_html_action.triggered.connect(self.export_html)
        self.export_pdf_action.triggered.connect(self.export_pdf)
        self.settings_action.triggered.connect(self.show_settings)

        # 看板列表信号
        self.dashboard_list.itemClicked.connect(self.on_dashboard_list_clicked)
        self.load_dashboard_btn.clicked.connect(self.load_selected_dashboard)
        self.delete_dashboard_btn.clicked.connect(self.delete_selected_dashboard)
    
    def load_initial_data(self):
        """加载初始数据"""
        try:
            self.load_dashboard_list()
            self.status_bar.showMessage("数据加载完成")
        except Exception as e:
            self.logger.error(f"加载初始数据失败: {e}")
            self.status_bar.showMessage(f"数据加载失败: {str(e)}")
    
    def load_dashboard_list(self):
        """加载看板列表"""
        try:
            dashboards = self.visualization_model.get_dashboard_configs()
            
            # 更新下拉框
            self.dashboard_combo.clear()
            self.dashboard_combo.addItem("请选择看板")
            
            # 更新列表
            self.dashboard_list.clear()
            
            for dashboard in dashboards:
                # 添加到下拉框
                self.dashboard_combo.addItem(dashboard['name'])
                
                # 添加到列表
                item = QListWidgetItem(dashboard['name'])
                item.setData(Qt.UserRole, dashboard)
                self.dashboard_list.addItem(item)
            
            self.logger.info(f"加载了 {len(dashboards)} 个看板")
            
        except Exception as e:
            self.logger.error(f"加载看板列表失败: {e}")
            QMessageBox.critical(self, "错误", f"加载看板列表失败: {str(e)}")
    
    def on_dashboard_selected(self, dashboard_name):
        """看板选择处理"""
        if dashboard_name and dashboard_name != "请选择看板":
            self.load_dashboard_by_name(dashboard_name)
    
    def on_dashboard_list_clicked(self, item):
        """看板列表点击处理"""
        dashboard_data = item.data(Qt.UserRole)
        if dashboard_data:
            self.load_dashboard(dashboard_data)
    
    def load_dashboard_by_name(self, name: str):
        """根据名称加载看板"""
        try:
            dashboards = self.visualization_model.get_dashboard_configs()
            for dashboard in dashboards:
                if dashboard['name'] == name:
                    self.load_dashboard(dashboard)
                    break
        except Exception as e:
            self.logger.error(f"加载看板失败: {e}")
            QMessageBox.critical(self, "错误", f"加载看板失败: {str(e)}")
    
    def load_dashboard(self, dashboard_data: Dict[str, Any]):
        """加载看板"""
        try:
            self.current_dashboard = dashboard_data
            # 🔧 修复：不再设置标题文本，标题已隐藏
            # self.dashboard_title.setText(dashboard_data['name'])

            # 显示进度条
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)  # 不确定进度

            # 渲染看板
            self.render_dashboard(dashboard_data)

            # 🔧 新增：更新按钮状态
            self.update_button_states()

            self.status_bar.showMessage(f"已加载看板: {dashboard_data['name']}")

        except Exception as e:
            self.logger.error(f"加载看板失败: {e}")
            QMessageBox.critical(self, "错误", f"加载看板失败: {str(e)}")
            self.progress_bar.setVisible(False)
    
    def render_dashboard(self, dashboard_data: Dict[str, Any]):
        """渲染看板"""
        try:
            layout = dashboard_data.get('layout', {})
            items = layout.get('items', [])
            
            # 生成HTML
            html_content = self.generate_dashboard_html(dashboard_data)
            
            # 🔧 关键修复：详细记录HTML中的网络请求
            self._log_network_requests_in_html(html_content, dashboard_data['name'])

            # 🔧 关键修复：清理HTML中的网络请求
            cleaned_html = self._clean_html_for_offline(html_content)

            # 设置HTML内容
            if WEB_ENGINE_AVAILABLE:
                self.web_view.setHtml(cleaned_html)
            else:
                self.web_view.setHtml(cleaned_html)

            self.dashboard_html = cleaned_html
            self.progress_bar.setVisible(False)
            
        except Exception as e:
            self.logger.error(f"渲染看板失败: {e}")
            self.progress_bar.setVisible(False)
            raise
    
    def generate_dashboard_html(self, dashboard_data: Dict[str, Any]) -> str:
        """生成看板HTML"""
        try:
            layout = dashboard_data.get('layout', {})
            items = layout.get('items', [])

            # 🔧 修复：正确获取画布大小，优先使用新格式，兼容旧格式
            canvas_width = layout.get('canvas_width', layout.get('canvas', {}).get('width', 1200))
            canvas_height = layout.get('canvas_height', layout.get('canvas', {}).get('height', 800))
            canvas = {'width': canvas_width, 'height': canvas_height}

            print(f"🎨 渲染看板: '{dashboard_data['name']}' - 画布大小={canvas_width}x{canvas_height}, 组件数量={len(items)}")

            # 🔍 详细调试：画布尺寸读取过程
            print(f"🔍 画布尺寸读取调试:")
            print(f"   新格式: canvas_width={layout.get('canvas_width')}, canvas_height={layout.get('canvas_height')}")
            canvas_old = layout.get('canvas', {})
            print(f"   旧格式: canvas.width={canvas_old.get('width') if canvas_old else None}, canvas.height={canvas_old.get('height') if canvas_old else None}")
            print(f"   最终使用: {canvas_width}x{canvas_height}")
            
            # 🔧 关键修复: 调整缩放算法，使看板填充整个查看器区域
            # 动态获取查看器可用区域大小
            try:
                # 尝试获取当前窗口大小
                if hasattr(self, 'viewer_frame') and self.viewer_frame:
                    viewer_rect = self.viewer_frame.geometry()
                    # 使用完整的查看器尺寸
                    viewer_width = viewer_rect.width()
                    viewer_height = viewer_rect.height()
                else:
                    # 备用默认尺寸
                    viewer_width = 1150
                    viewer_height = 850
            except Exception as e:
                self.logger.warning(f"无法获取查看器尺寸，使用默认值: {e}")
                viewer_width = 1150
                viewer_height = 850

            # 🔍 详细调试：查看器尺寸获取
            print(f"🔍 查看器尺寸调试:")
            print(f"   查看器尺寸: {viewer_width}x{viewer_height}")

            # 🎯 优化缩放计算：填充整个区域，不留边距
            available_width = viewer_width
            available_height = viewer_height

            # 🔍 详细调试：缩放比例计算过程
            print(f"🔍 缩放比例计算调试:")
            print(f"   可用区域: {available_width}x{available_height}")
            print(f"   画布尺寸: {canvas_width}x{canvas_height}")

            # 计算缩放比例
            # 防止除以零
            scale_x = available_width / canvas_width if canvas_width > 0 else 1
            scale_y = available_height / canvas_height if canvas_height > 0 else 1
            # 🔧 关键修复: 从 'min' (fit) 改为 'max' (cover) 来填充空间，允许滚动
            scale = max(scale_x, scale_y)

            print(f"   计算缩放: X={scale_x:.3f}, Y={scale_y:.3f}")
            print(f"   初始缩放 (策略: cover): {scale:.3f}")

            # 计算缩放后的尺寸
            scaled_width = int(canvas_width * scale)
            scaled_height = int(canvas_height * scale)

            # 🔍 详细调试：最终渲染尺寸
            print(f"🔍 最终渲染尺寸调试:")
            print(f"   最终缩放: {scale:.3f} ({int(scale*100)}%)")
            print(f"   缩放后尺寸: {scaled_width}x{scaled_height}")

            # 判断显示效果
            if scale < 0.5:
                print(f"   ⚠️  显示效果: 较小，可能需要放大查看")
            elif scale < 0.8:
                print(f"   ✅ 显示效果: 适中，可读性良好")
            else:
                print(f"   ✅ 显示效果: 较大，充分利用空间")

            print(f"🔍 自适应计算详情:")
            print(f"  - 查看器尺寸: {viewer_width}x{viewer_height}")
            print(f"  - 可用区域: {available_width}x{available_height}")
            print(f"  - 画布尺寸: {canvas_width}x{canvas_height}")
            print(f"  - 计算缩放: X={scale_x:.3f}, Y={scale_y:.3f}")
            print(f"  - 最终缩放: {scale:.3f}")
            print(f"  - 缩放后尺寸: {scaled_width}x{scaled_height}")

            # 🔍 详细调试：CSS样式应用过程
            print(f"🔍 CSS样式应用调试:")
            print(f"   容器原始尺寸: {canvas_width}px x {canvas_height}px")
            print(f"   CSS transform: scale({scale})")
            print(f"   CSS transform-origin: center center")
            print(f"   容器在页面中的实际显示尺寸: {scaled_width}px x {scaled_height}px")
            print(f"   CSS布局: position: absolute, top: 50%, left: 50%")
            print(f"   CSS定位: transform: scale({scale}) translate(-50%, -50%)")
            print(f"   JavaScript初始缩放: {scale}")
            print(f"   JavaScript会在DOM加载后重新应用定位和缩放")
            print(f"   预期效果: 看板应该在查看器中央显示，缩放按钮正常工作")

            html = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <title>{dashboard_data['name']}</title>
                <style>
                    html, body {{
                        height: 100%;
                        margin: 0;
                        padding: 0;
                        overflow: hidden;
                    }}
                    body {{
                        font-family: 'Microsoft YaHei', Arial, sans-serif;
                        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
                    }}

                    .dashboard-wrapper {{
                        width: 100%;
                        height: 100%;
                        overflow: auto;
                        cursor: grab;
                        /* 🔧 关键修复: 使用Grid实现完美居中 */
                        display: grid;
                        place-items: center;
                    }}

                    .dashboard-wrapper:active {{
                        cursor: grabbing;
                    }}

                    .dashboard-container {{
                        width: {canvas_width}px;
                        height: {canvas_height}px;
                        background-color: white;
                        border: 2px solid #e1e8ed;
                        /* 🔧 修复: Grid负责居中, transform不再需要translate */
                        transform: scale({scale});
                        transform-origin: center center;
                        box-shadow:
                            0 8px 32px rgba(0,0,0,0.12),
                            0 2px 8px rgba(0,0,0,0.08);
                        border-radius: 8px;
                        transition: transform 0.3s ease, box-shadow 0.3s ease;
                    }}

                    .dashboard-container:hover {{
                        box-shadow:
                            0 12px 40px rgba(0,0,0,0.15),
                            0 4px 12px rgba(0,0,0,0.1);
                    }}

                    /* 响应式设计 */
                    @media (max-width: 1200px) {{
                        .dashboard-wrapper {{
                            padding: 10px;
                        }}
                        .zoom-controls {{
                            top: 10px;
                            right: 10px;
                        }}
                    }}

                    @media (max-width: 768px) {{
                        .dashboard-wrapper {{
                            padding: 5px;
                        }}
                        .zoom-controls {{
                            position: relative;
                            top: auto;
                            right: auto;
                            margin-bottom: 10px;
                        }}
                    }}
                    .dashboard-item {{
                        position: absolute;
                        /* 基础样式由内联样式控制，避免冲突 */
                    }}
                    .text-item {{
                        /* 文本框样式由内联样式完全控制 */
                    }}
                    .chart-item {{
                        /* 图表样式由内联样式完全控制 */
                    }}
                    .chart-placeholder {{
                        width: 100%;
                        height: 100%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        background-color: #f9f9f9;
                        border: 2px dashed #ccc;
                    }}

                    /* 🎨 优化缩放控制按钮样式 */
                    .zoom-controls {{
                        position: fixed;
                        top: 15px;
                        right: 15px;
                        z-index: 1000;
                        background: rgba(255, 255, 255, 0.95);
                        border: 1px solid rgba(0,0,0,0.1);
                        border-radius: 12px;
                        padding: 12px;
                        box-shadow:
                            0 8px 32px rgba(0,0,0,0.12),
                            0 2px 8px rgba(0,0,0,0.08);
                        backdrop-filter: blur(10px);
                        transition: all 0.3s ease;
                    }}

                    .zoom-controls:hover {{
                        background: rgba(255, 255, 255, 0.98);
                        box-shadow:
                            0 12px 40px rgba(0,0,0,0.15),
                            0 4px 12px rgba(0,0,0,0.1);
                    }}

                    .zoom-btn {{
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        color: white;
                        border: none;
                        padding: 10px 14px;
                        margin: 3px;
                        border-radius: 8px;
                        cursor: pointer;
                        font-size: 14px;
                        font-weight: 500;
                        transition: all 0.2s ease;
                        box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
                    }}

                    .zoom-btn:hover {{
                        background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
                        transform: translateY(-1px);
                        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
                    }}

                    .zoom-btn:active {{
                        transform: translateY(0);
                        box-shadow: 0 2px 6px rgba(102, 126, 234, 0.3);
                    }}

                    .zoom-info {{
                        font-size: 12px;
                        color: #4a5568;
                        text-align: center;
                        margin-top: 8px;
                        font-weight: 500;
                        background: rgba(74, 85, 104, 0.1);
                        padding: 4px 8px;
                        border-radius: 6px;
                    }}
                </style>
                <script>
                    let currentScale = {scale};
                    let isDragging = false;
                    let startX, startY, scrollLeft, scrollTop;

                    function zoomIn() {{
                        currentScale = Math.min(currentScale * 1.2, 3.0);
                        updateZoom();
                    }}

                    function zoomOut() {{
                        currentScale = Math.max(currentScale / 1.2, 0.1);
                        updateZoom();
                    }}

                    function resetZoom() {{
                        currentScale = {scale};
                        updateZoom();
                    }}

                    function fitToWindow() {{
                        const wrapper = document.querySelector('.dashboard-wrapper');
                        const container = document.querySelector('.dashboard-container');
                        if (wrapper && container) {{
                            const wrapperRect = wrapper.getBoundingClientRect();
                            const containerWidth = {canvas_width};
                            const containerHeight = {canvas_height};

                            // 🔧 关键修复: 计算缩放比例以填充整个区域 (cover模式)
                            const scaleX = wrapperRect.width / containerWidth;
                            const scaleY = wrapperRect.height / containerHeight;

                            let newScale = Math.max(scaleX, scaleY);

                            currentScale = newScale;
                            updateZoom();
                        }}
                    }}

                    function updateZoom() {{
                        const container = document.querySelector('.dashboard-container');
                        const info = document.querySelector('.zoom-info');
                        if (container) {{
                            /* 🔧 关键修复: Grid负责居中, JS仅需负责缩放 */
                            container.style.transform = `scale(${{currentScale}})`;
                        }}
                        if (info) {{
                            const percentage = Math.round(currentScale * 100);
                            info.textContent = `缩放: ${{percentage}}%`;

                            // 🎨 根据缩放比例调整信息颜色
                            if (percentage < 50) {{
                                info.style.color = '#e53e3e';
                            }} else if (percentage > 120) {{
                                info.style.color = '#38a169';
                            }} else {{
                                info.style.color = '#4a5568';
                            }}
                        }}
                    }}

                    // 鼠标拖拽滚动
                    document.addEventListener('DOMContentLoaded', function() {{
                        const wrapper = document.querySelector('.dashboard-wrapper');

                        wrapper.addEventListener('mousedown', function(e) {{
                            if (e.target.closest('.zoom-controls')) return;
                            isDragging = true;
                            startX = e.pageX - wrapper.offsetLeft;
                            startY = e.pageY - wrapper.offsetTop;
                            scrollLeft = wrapper.scrollLeft;
                            scrollTop = wrapper.scrollTop;
                            wrapper.style.cursor = 'grabbing';
                        }});

                        wrapper.addEventListener('mouseleave', function() {{
                            isDragging = false;
                            wrapper.style.cursor = 'default';
                        }});

                        wrapper.addEventListener('mouseup', function() {{
                            isDragging = false;
                            wrapper.style.cursor = 'default';
                        }});

                        wrapper.addEventListener('mousemove', function(e) {{
                            if (!isDragging) return;
                            e.preventDefault();
                            const x = e.pageX - wrapper.offsetLeft;
                            const y = e.pageY - wrapper.offsetTop;
                            const walkX = (x - startX) * 2;
                            const walkY = (y - startY) * 2;
                            wrapper.scrollLeft = scrollLeft - walkX;
                            wrapper.scrollTop = scrollTop - walkY;
                        }});

                        // 鼠标滚轮缩放
                        wrapper.addEventListener('wheel', function(e) {{
                            if (e.ctrlKey) {{
                                e.preventDefault();
                                if (e.deltaY < 0) {{
                                    zoomIn();
                                }} else {{
                                    zoomOut();
                                }}
                            }}
                        }});

                        // 初始化缩放信息
                        updateZoom();
                    }});
                </script>
            </head>
            <body>
                <div class="zoom-controls">
                    <button class="zoom-btn" onclick="zoomIn()" title="放大 (Ctrl + 滚轮向上)">🔍+</button>
                    <button class="zoom-btn" onclick="zoomOut()" title="缩小 (Ctrl + 滚轮向下)">🔍-</button>
                    <button class="zoom-btn" onclick="resetZoom()" title="重置到最佳大小">↻</button>
                    <button class="zoom-btn" onclick="fitToWindow()" title="适应窗口大小">⊞</button>
                    <div class="zoom-info">缩放: {int(scale*100)}%</div>
                </div>
                <div class="dashboard-wrapper">
                    <div class="dashboard-container">
            """
            
            # 添加看板项目
            for item in items:
                item_type = item.get('type', 'unknown')
                x = item.get('x', 0)
                y = item.get('y', 0)
                width = item.get('width', 200)
                height = item.get('height', 150)
                
                if item_type == 'text':
                    # 🔧 修复：完整渲染TextItem的所有属性
                    text_content = item.get('text', '文本')

                    # 解析字体设置
                    font_data = item.get('font', {})
                    font_family = font_data.get('family', 'Microsoft YaHei')
                    font_size = font_data.get('size', 12)
                    font_bold = font_data.get('bold', False)
                    font_italic = font_data.get('italic', False)
                    font_underline = font_data.get('underline', False)

                    # 解析颜色设置
                    colors_data = item.get('colors', {})
                    text_color_data = colors_data.get('text', {'r': 0, 'g': 0, 'b': 0, 'a': 255})
                    bg_color_data = colors_data.get('background', {'r': 255, 'g': 255, 'b': 230, 'a': 200})

                    # 构建CSS样式
                    text_color = f"rgba({text_color_data['r']}, {text_color_data['g']}, {text_color_data['b']}, {text_color_data['a']/255})"
                    bg_color = f"rgba({bg_color_data['r']}, {bg_color_data['g']}, {bg_color_data['b']}, {bg_color_data['a']/255})"

                    font_weight = 'bold' if font_bold else 'normal'
                    font_style = 'italic' if font_italic else 'normal'
                    text_decoration = 'underline' if font_underline else 'none'

                    # 生成完整的文本框HTML
                    html += f"""
                    <div class="dashboard-item text-item" style="
                        left: {x}px;
                        top: {y}px;
                        width: {width}px;
                        height: {height}px;
                        font-family: '{font_family}', 'Microsoft YaHei', Arial, sans-serif;
                        font-size: {font_size}px;
                        font-weight: {font_weight};
                        font-style: {font_style};
                        text-decoration: {text_decoration};
                        color: {text_color};
                        background-color: {bg_color};
                        border: 1px solid rgba(200, 200, 200, 0.8);
                        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                        padding: 10px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        text-align: center;
                        word-wrap: break-word;
                        overflow: hidden;
                    ">
                        {text_content}
                    </div>
                    """

                    print(f"✅ 渲染文本框: '{text_content}' - 字体={font_family} {font_size}px, 颜色={text_color}, 背景={bg_color}")
                elif item_type == 'chart':
                    # 🔧 修复：改进图表组件渲染
                    chart_data = item.get('data', {})
                    chart_name = chart_data.get('name', '图表')
                    chart_type = chart_data.get('chart_type', '未知')

                    # 尝试渲染真实图表
                    chart_html = self._render_chart_for_dashboard(chart_data, width, height)

                    # 🔧 修复：优化图表容器HTML，支持高DPI渲染
                    # 将图表HTML转义以安全地放入srcdoc属性
                    escaped_chart_html = html_parser.escape(chart_html)

                    # 🔧 新增：获取设备像素比例用于iframe优化
                    try:
                        from src.utils.dpi_adapter import dpi_adapter
                        device_pixel_ratio = dpi_adapter.get_device_pixel_ratio()
                        # 计算iframe的实际显示尺寸
                        iframe_width = width
                        iframe_height = height

                        # 如果是高DPI设备，调整iframe的渲染质量
                        iframe_style = f"width: 100%; height: 100%; border: none;"
                        if device_pixel_ratio > 1.0:
                            # 添加高DPI优化样式
                            iframe_style += f" image-rendering: -webkit-optimize-contrast; image-rendering: crisp-edges;"

                    except Exception as e:
                        print(f"   ⚠️ iframe DPI优化失败: {e}")
                        iframe_style = "width: 100%; height: 100%; border: none;"

                    html += f"""
                    <div class="dashboard-item chart-item" style="
                        left: {x}px;
                        top: {y}px;
                        width: {width}px;
                        height: {height}px;
                        border: 1px solid #ddd;
                        background-color: white;
                        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                        overflow: hidden;
                        /* 🔧 新增：高DPI容器优化 */
                        image-rendering: -webkit-optimize-contrast;
                        image-rendering: crisp-edges;
                    ">
                        <iframe srcdoc="{escaped_chart_html}"
                                style="{iframe_style}"
                                sandbox="allow-scripts allow-same-origin"
                                loading="eager">
                        </iframe>
                    </div>
                    """

                    print(f"✅ 渲染图表: '{chart_name}' - 类型={chart_type}, 尺寸={width}x{height}")
            
            html += """
                    </div>
                </div>
            </body>
            </html>
            """
            
            return html
            
        except Exception as e:
            self.logger.error(f"生成看板HTML失败: {e}")
            return self.get_error_html(str(e))
    
    def get_default_html(self) -> str:
        """获取默认HTML"""
        return """
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>看板查看器</title>
            <style>
                body {
                    margin: 0;
                    padding: 50px;
                    font-family: 'Microsoft YaHei', Arial, sans-serif;
                    background-color: #f5f5f5;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    height: 100vh;
                }
                .welcome {
                    text-align: center;
                    color: #666;
                }
            </style>
        </head>
        <body>
            <div class="welcome">
                <h2>欢迎使用看板查看器</h2>
                <p>请从左侧列表选择要查看的看板</p>
            </div>
        </body>
        </html>
        """
    
    def get_error_html(self, error_msg: str) -> str:
        """获取错误HTML"""
        return f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>错误</title>
            <style>
                body {{
                    margin: 0;
                    padding: 50px;
                    font-family: 'Microsoft YaHei', Arial, sans-serif;
                    background-color: #f5f5f5;
                }}
                .error {{
                    background-color: #fff;
                    border: 1px solid #f5c6cb;
                    color: #721c24;
                    padding: 20px;
                    border-radius: 4px;
                }}
            </style>
        </head>
        <body>
            <div class="error">
                <h3>加载看板时发生错误</h3>
                <p>{error_msg}</p>
            </div>
        </body>
        </html>
        """
    
    def load_selected_dashboard(self):
        """加载选中的看板"""
        current_item = self.dashboard_list.currentItem()
        if current_item:
            dashboard_data = current_item.data(Qt.UserRole)
            self.load_dashboard(dashboard_data)
    
    def delete_selected_dashboard(self):
        """删除选中的看板"""
        current_item = self.dashboard_list.currentItem()
        if current_item:
            dashboard_data = current_item.data(Qt.UserRole)
            reply = QMessageBox.question(
                self, "确认删除", 
                f"确定要删除看板 '{dashboard_data['name']}' 吗？",
                QMessageBox.Yes | QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                try:
                    self.visualization_model.delete_dashboard_config(dashboard_data['id'])
                    self.load_dashboard_list()
                    self.status_bar.showMessage(f"已删除看板: {dashboard_data['name']}")
                except Exception as e:
                    QMessageBox.critical(self, "错误", f"删除看板失败: {str(e)}")
    
    def refresh_data(self):
        """刷新数据"""
        self.load_dashboard_list()
        self.status_bar.showMessage("数据已刷新")

    def refresh_dashboard(self):
        """🔧 新增：刷新当前看板内容"""
        try:
            if not self.current_dashboard:
                self.logger.warning("没有当前看板可以刷新")
                return

            dashboard_name = self.current_dashboard.get('name', '未知看板')
            self.logger.info(f"刷新看板: {dashboard_name}")

            # 🔧 修复：从数据库重新加载看板配置，确保获取最新的完整配置
            from src.models.visualization import VisualizationModel
            visualization_model = VisualizationModel(self.db_path)
            dashboards = visualization_model.get_dashboard_configs()

            updated_dashboard = None
            for dashboard in dashboards:
                if dashboard['name'] == dashboard_name:
                    updated_dashboard = dashboard
                    break

            if updated_dashboard:
                # 更新当前看板数据
                self.current_dashboard = updated_dashboard

                # 重新渲染看板
                self.render_dashboard(updated_dashboard)

                self.logger.info(f"看板 '{dashboard_name}' 刷新完成")
                self.status_bar.showMessage(f"看板 '{dashboard_name}' 已刷新")
            else:
                self.logger.warning(f"未找到看板配置: {dashboard_name}")
                self.status_bar.showMessage(f"刷新失败：未找到看板 '{dashboard_name}'")

        except Exception as e:
            self.logger.error(f"刷新看板失败: {e}")
            self.status_bar.showMessage(f"刷新失败: {str(e)}")

    def toggle_fullscreen(self):
        """切换全屏"""
        if self.isFullScreen():
            self.showNormal()
            self.fullscreen_action.setText("全屏")
        else:
            self.showFullScreen()
            self.fullscreen_action.setText("退出全屏")
    
    def export_html(self):
        """导出HTML"""
        if not self.current_dashboard:
            QMessageBox.warning(self, "警告", "请先选择要导出的看板")
            return
        
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出HTML", 
                f"{self.current_dashboard['name']}.html",
                "HTML文件 (*.html)"
            )
            
            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(self.dashboard_html)
                
                QMessageBox.information(self, "成功", f"看板已导出到: {file_path}")
                self.status_bar.showMessage(f"已导出HTML: {file_path}")
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"导出HTML失败: {str(e)}")
    
    def export_pdf(self):
        """导出PDF"""
        if not self.current_dashboard:
            QMessageBox.warning(self, "警告", "请先选择要导出的看板")
            return

        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出PDF",
                f"{self.current_dashboard['name']}.pdf",
                "PDF文件 (*.pdf)"
            )

            if file_path:
                # 🚀 高性能：使用快速PDF导出器生成PDF
                from src.utils.fast_pdf_exporter import FastPDFExporter
                pdf_exporter = FastPDFExporter(self)
                success = pdf_exporter.export_dashboard_to_pdf(
                    self.current_dashboard,
                    self.dashboard_html,
                    file_path
                )

                if success:
                    self.status_bar.showMessage(f"已导出PDF: {file_path}")
                else:
                    QMessageBox.critical(self, "错误", "PDF导出失败")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"导出PDF失败: {str(e)}")
            self.logger.error(f"PDF导出异常: {e}")
    
    def _render_chart_for_dashboard(self, chart_data: Dict[str, Any], width: int, height: int) -> str:
        """🔧 修复：为看板渲染图表，支持高DPI和清晰度优化"""
        try:
            from src.utils.chart_renderer import ChartRenderer
            from src.utils.dpi_adapter import dpi_adapter
            import sqlite3
            import pandas as pd

            # 获取图表配置
            config = chart_data.get('config', {})
            if not config:
                return self._get_chart_placeholder(chart_data.get('name', '图表'), "配置缺失")

            # 🔧 关键修复：创建配置副本，避免修改原始配置
            config = config.copy()

            # 🔧 关键修复：确保样式配置被正确保持
            original_show_labels = config.get('show_labels', False)
            self.logger.debug(f"看板图表 '{chart_data.get('name', '未知')}' 原始show_labels配置: {original_show_labels}")

            # 执行查询获取数据
            query = config.get('query', '')
            if not query:
                return self._get_chart_placeholder(chart_data.get('name', '图表'), "查询语句缺失")

            # 连接数据库并执行查询
            conn = sqlite3.connect(self.db_path)
            data = pd.read_sql_query(query, conn)
            conn.close()

            if data.empty:
                return self._get_chart_placeholder(chart_data.get('name', '图表'), "暂无数据")

            # 🔧 新增：获取设备像素比例和优化配置
            try:
                device_pixel_ratio = dpi_adapter.get_device_pixel_ratio()
                print(f"   📱 看板图表渲染 - 设备像素比例: {device_pixel_ratio}")

                # 🔧 修复：计算高DPI渲染尺寸
                logical_width = width - 10  # 保留边距
                logical_height = height - 10
                render_width, render_height = dpi_adapter.calculate_render_size(logical_width, logical_height)

                print(f"   📏 看板图表尺寸 - 逻辑: {logical_width}x{logical_height}, 渲染: {render_width}x{render_height}")

                # 使用渲染尺寸进行图表生成
                config['width'] = render_width
                config['height'] = render_height

                # 🔧 新增：应用DPI优化配置
                config = dpi_adapter.get_chart_config_adjustments(config, logical_width, logical_height)

            except Exception as e:
                print(f"   ⚠️ DPI适配失败，使用默认配置: {e}")
                config['width'] = width - 10
                config['height'] = height - 10

            # 🔧 关键修复：确保原始样式配置不被覆盖
            config['show_labels'] = original_show_labels

            # 渲染图表
            renderer = ChartRenderer()
            chart_html = renderer.render_chart(config, data)

            return chart_html

        except Exception as e:
            self.logger.error(f"渲染看板图表失败: {e}")
            return self._get_chart_placeholder(chart_data.get('name', '图表'), f"渲染失败: {str(e)}")

    def _get_chart_placeholder(self, chart_name: str, message: str) -> str:
        """获取图表占位符HTML"""
        return f"""
        <div class="chart-placeholder">
            <div style="text-align: center;">
                <h4>{chart_name}</h4>
                <p style="color: #666;">{message}</p>
            </div>
        </div>
        """

    def show_settings(self):
        """显示设置"""
        QMessageBox.information(self, "提示", "设置功能正在开发中")

    # 🔧 新增：添加到主界面和移除功能的处理方法
    def add_dashboard_to_main(self):
        """将当前选中的看板添加到主界面"""
        try:
            if not self.current_dashboard:
                QMessageBox.warning(self, "提示", "请先选择一个看板")
                return

            dashboard_name = self.current_dashboard['name']

            # 检查是否已经添加
            if dashboard_name in self._dashboard_main_status and self._dashboard_main_status[dashboard_name]:
                QMessageBox.information(self, "提示", f"看板 '{dashboard_name}' 已经在主界面中")
                return

            # 发射信号，请求主界面添加看板
            self.dashboard_add_to_main_requested.emit(self.current_dashboard)

            # 🔧 修复：调用模型更新数据库
            self.visualization_model.set_dashboard_main_status(self.current_dashboard['id'], True)
            self._dashboard_main_status[dashboard_name] = True

            # 更新按钮状态
            self.update_button_states()

            self.status_bar.showMessage(f"已请求将看板 '{dashboard_name}' 添加到主界面")
            self.logger.info(f"请求添加看板到主界面: {dashboard_name}")

        except Exception as e:
            self.logger.error(f"添加看板到主界面失败: {e}")
            QMessageBox.critical(self, "错误", f"添加看板到主界面失败:\n{str(e)}")

    def remove_dashboard_from_main(self):
        """从主界面移除当前选中的看板"""
        try:
            if not self.current_dashboard:
                QMessageBox.warning(self, "提示", "请先选择一个看板")
                return

            dashboard_name = self.current_dashboard['name']

            # 检查是否在主界面中
            if dashboard_name not in self._dashboard_main_status or not self._dashboard_main_status[dashboard_name]:
                QMessageBox.information(self, "提示", f"看板 '{dashboard_name}' 不在主界面中")
                return

            # 发射信号，请求主界面移除看板
            self.dashboard_remove_from_main_requested.emit(self.current_dashboard)

            # 🔧 修复：调用模型更新数据库
            self.visualization_model.set_dashboard_main_status(self.current_dashboard['id'], False)
            self._dashboard_main_status[dashboard_name] = False

            # 更新按钮状态
            self.update_button_states()

            self.status_bar.showMessage(f"已请求从主界面移除看板 '{dashboard_name}'")
            self.logger.info(f"请求从主界面移除看板: {dashboard_name}")

        except Exception as e:
            self.logger.error(f"从主界面移除看板失败: {e}")
            QMessageBox.critical(self, "错误", f"从主界面移除看板失败:\n{str(e)}")

    def update_button_states(self):
        """更新按钮状态"""
        try:
            if not self.current_dashboard:
                # 没有选中看板时，禁用所有按钮
                self.add_to_main_action.setEnabled(False)
                self.remove_from_main_action.setEnabled(False)
                return

            # 🔧 修复：当选中看板时，从缓存的状态字典中获取信息
            dashboard_name = self.current_dashboard['name']
            in_main = self._dashboard_main_status.get(dashboard_name, False)

            self.add_to_main_action.setEnabled(not in_main)
            self.remove_from_main_action.setEnabled(in_main)

        except Exception as e:
            self.logger.error(f"更新按钮状态失败: {e}")

    def is_dashboard_in_main(self, dashboard_data: Dict[str, Any]) -> bool:
        """检查看板是否已添加到主界面"""
        # 🔧 修复: 从内存状态中获取信息
        if not dashboard_data:
            return False
        return self._dashboard_main_status.get(dashboard_data.get('name'), False)

    def set_dashboard_main_status(self, dashboard_name: str, in_main: bool):
        """设置看板在主界面的状态（由主界面调用）"""
        try:
            # 🔧 修复：更新状态跟踪
            self._dashboard_main_status[dashboard_name] = in_main

            # 如果当前显示的就是这个看板，立即更新按钮状态
            if self.current_dashboard and self.current_dashboard.get('name') == dashboard_name:
                self.add_to_main_action.setEnabled(not in_main)
                self.remove_from_main_action.setEnabled(in_main)

                # 更新状态栏消息
                status_msg = f"看板 '{dashboard_name}' " + ("已在主界面中" if in_main else "不在主界面中")
                self.status_bar.showMessage(status_msg)

        except Exception as e:
            self.logger.error(f"设置看板主界面状态失败: {e}")

    def load_all_dashboard_main_statuses(self):
        """从数据库加载所有看板的主界面状态"""
        try:
            dashboards = self.visualization_model.get_dashboard_configs()
            for db in dashboards:
                self._dashboard_main_status[db['name']] = bool(db.get('is_on_main', 0))
            self.logger.info(f"成功加载所有看板的主界面状态: {self._dashboard_main_status}")
        except Exception as e:
            self.logger.error(f"加载所有看板的主界面状态失败: {e}")
