#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
最终修复验证工具
验证所有WebEngine网络崩溃修复是否生效

修复内容：
1. run.py中的强制WebEngine网络禁用
2. 看板查看器中的WebEngine离线配置
3. HTML内容的网络请求清理
4. 离线ECharts生成器备用方案
"""

import os
import sys
import logging
from datetime import datetime

# 设置项目根目录
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def test_run_py_webengine_config():
    """测试run.py中的WebEngine配置"""
    logger = logging.getLogger(__name__)
    
    try:
        # 导入run.py会自动执行WebEngine配置
        import run
        
        # 检查关键环境变量
        critical_vars = [
            'QTWEBENGINE_DISABLE_SANDBOX',
            'QTWEBENGINE_DISABLE_NETWORK',
            'QTWEBENGINE_DISABLE_BACKGROUND_NETWORKING',
            'QTWEBENGINE_CHROMIUM_FLAGS'
        ]
        
        set_count = 0
        for var in critical_vars:
            value = os.environ.get(var)
            if value:
                logger.info(f"✅ {var}: {value[:50]}...")
                set_count += 1
            else:
                logger.warning(f"⚠️ {var}: 未设置")
        
        if set_count >= 3:
            logger.info("✅ run.py WebEngine配置成功")
            return True
        else:
            logger.warning("⚠️ run.py WebEngine配置不完整")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试run.py WebEngine配置失败: {e}")
        return False

def test_dashboard_viewer_offline():
    """测试看板查看器离线配置"""
    logger = logging.getLogger(__name__)
    
    try:
        from src.views.dashboard_viewer import DashboardViewer
        
        # 检查是否有离线配置方法
        if hasattr(DashboardViewer, '_configure_webengine_offline'):
            logger.info("✅ 看板查看器有WebEngine离线配置方法")
        else:
            logger.warning("⚠️ 看板查看器缺少WebEngine离线配置方法")
            return False
        
        # 检查是否有HTML清理方法
        if hasattr(DashboardViewer, '_clean_html_for_offline'):
            logger.info("✅ 看板查看器有HTML清理方法")
        else:
            logger.warning("⚠️ 看板查看器缺少HTML清理方法")
            return False
        
        logger.info("✅ 看板查看器离线配置完整")
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试看板查看器离线配置失败: {e}")
        return False

def test_chart_renderer_offline():
    """测试图表渲染器离线配置"""
    logger = logging.getLogger(__name__)
    
    try:
        from src.utils.chart_renderer import ChartRenderer
        
        # 创建图表渲染器
        renderer = ChartRenderer()
        
        # 检查是否有离线生成器
        if hasattr(renderer, 'offline_generator') and renderer.offline_generator:
            logger.info("✅ 图表渲染器有离线生成器")
        else:
            logger.warning("⚠️ 图表渲染器缺少离线生成器")
            return False
        
        # 检查是否有HTML清理方法
        if hasattr(renderer, '_clean_html_network_requests'):
            logger.info("✅ 图表渲染器有HTML清理方法")
        else:
            logger.warning("⚠️ 图表渲染器缺少HTML清理方法")
            return False
        
        logger.info("✅ 图表渲染器离线配置完整")
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试图表渲染器离线配置失败: {e}")
        return False

def test_offline_echarts_generator():
    """测试离线ECharts生成器"""
    logger = logging.getLogger(__name__)
    
    try:
        from src.utils.offline_echarts_generator import OfflineEChartsGenerator
        import pandas as pd
        
        # 创建生成器
        generator = OfflineEChartsGenerator()
        
        # 创建测试数据
        test_data = pd.DataFrame({
            'category': ['最终测试A', '最终测试B'],
            'value': [50, 30]
        })
        
        # 测试生成
        config = {'title': '最终修复验证图表', 'width': 400, 'height': 300}
        html = generator.generate_bar_chart(test_data, config)
        
        if html and len(html) > 1000:
            # 检查是否包含网络链接
            network_count = html.count('https://') + html.count('http://') + html.count('file://')
            
            if network_count == 0:
                logger.info("✅ 离线ECharts生成器完全离线")
                
                # 保存测试文件
                test_file = os.path.join(project_root, 'temp', 'final_verification_chart.html')
                os.makedirs(os.path.dirname(test_file), exist_ok=True)
                
                with open(test_file, 'w', encoding='utf-8') as f:
                    f.write(html)
                
                logger.info(f"测试文件已保存: {test_file}")
                return True
            else:
                logger.warning(f"⚠️ 离线ECharts生成器包含{network_count}个网络链接")
                return False
        else:
            logger.error("❌ 离线ECharts生成器生成失败")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试离线ECharts生成器失败: {e}")
        return False

def test_pyecharts_config():
    """测试pyecharts配置"""
    logger = logging.getLogger(__name__)
    
    try:
        from pyecharts.globals import CurrentConfig
        
        logger.info(f"pyecharts ONLINE_HOST: '{CurrentConfig.ONLINE_HOST}'")
        
        if CurrentConfig.ONLINE_HOST == "":
            logger.info("✅ pyecharts完全离线模式")
            return True
        else:
            logger.warning(f"⚠️ pyecharts仍有配置: {CurrentConfig.ONLINE_HOST}")
            return False
            
    except ImportError:
        logger.error("❌ pyecharts未安装")
        return False
    except Exception as e:
        logger.error(f"❌ 测试pyecharts配置失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("最终修复验证工具")
    print("=" * 60)
    print(f"验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    print("验证的修复内容:")
    print("1. run.py中的强制WebEngine网络禁用")
    print("2. 看板查看器中的WebEngine离线配置")
    print("3. HTML内容的网络请求清理")
    print("4. 离线ECharts生成器备用方案")
    print("5. pyecharts完全离线配置")
    print()
    
    # 确保temp目录存在
    os.makedirs(os.path.join(project_root, 'temp'), exist_ok=True)
    
    # 设置日志
    logger = setup_logging()
    logger.info("开始最终修复验证")
    
    # 测试步骤
    tests = [
        ("run.py WebEngine配置", test_run_py_webengine_config),
        ("看板查看器离线配置", test_dashboard_viewer_offline),
        ("图表渲染器离线配置", test_chart_renderer_offline),
        ("离线ECharts生成器", test_offline_echarts_generator),
        ("pyecharts配置", test_pyecharts_config),
    ]
    
    success_count = 0
    for test_name, test_func in tests:
        print(f"[{success_count + 1}/{len(tests)}] {test_name}...")
        try:
            if test_func():
                print(f"✅ {test_name} - 通过")
                success_count += 1
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {e}")
            logger.error(f"{test_name}异常: {e}")
    
    print()
    print("=" * 60)
    print("最终验证结果")
    print("=" * 60)
    
    if success_count == len(tests):
        print("🎉 所有修复都验证通过！")
        print()
        print("修复状态:")
        print("✅ WebEngine强制网络禁用已设置")
        print("✅ 看板查看器离线配置完整")
        print("✅ 图表渲染器离线配置完整")
        print("✅ 离线ECharts生成器工作正常")
        print("✅ pyecharts完全离线模式")
        print()
        print("现在应该能够彻底解决WebEngine网络服务崩溃问题：")
        print("- 在最底层禁用了WebEngine的所有网络功能")
        print("- 看板查看器有独立的离线配置")
        print("- HTML内容会被清理，移除所有网络请求")
        print("- 即使pyecharts失败，也有完全离线的备用方案")
        print()
        print("🚀 现在可以安全运行 run1.bat！")
    else:
        print(f"⚠️ 部分修复验证失败 ({success_count}/{len(tests)} 通过)")
        print()
        print("失败的修复可能导致问题仍然存在。")
        print("建议检查相关代码文件是否正确修改。")
    
    print()
    input("按回车键退出...")

if __name__ == "__main__":
    main()
