#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
运行脚本 - 启动试验问题管控工具
优化版本：支持多核处理器，减少不必要的信息通知
"""

import os
import sys
import logging
import traceback
import multiprocessing
from concurrent.futures import ThreadPoolExecutor
import threading

# 🔧 关键修复：程序启动时立即配置完全离线模式
def configure_offline_environment():
    """配置完全离线环境，防止网络请求导致崩溃"""
    try:
        # 1. 强化WebEngine环境变量
        offline_env_vars = {
            'QTWEBENGINE_DISABLE_SANDBOX': '1',
            'QTWEBENGINE_DISABLE_GPU_SANDBOX': '1',
            'QTWEBENGINE_REMOTE_DEBUGGING': '0',
            'QTWEBENGINE_DISABLE_NETWORK': '1',
            'QTWEBENGINE_DISABLE_BACKGROUND_NETWORKING': '1',
            'QTWEBENGINE_DISABLE_SYNC': '1',
            'QTWEBENGINE_DISABLE_GPU': '1',
            'QTWEBENGINE_DISABLE_SOFTWARE_RASTERIZER': '1',
            'QTWEBENGINE_DISABLE_ACCELERATED_2D_CANVAS': '1',
            'QTWEBENGINE_DISABLE_WEBGL': '1',
            'QTWEBENGINE_DISABLE_EXTENSIONS': '1',
            'QTWEBENGINE_DISABLE_PLUGINS': '1',
        }

        for var, value in offline_env_vars.items():
            os.environ[var] = value

        print("✅ WebEngine离线环境变量已设置")

        # 2. 强制配置pyecharts完全离线模式（避免file://协议导致的WebEngine问题）
        try:
            from pyecharts.globals import CurrentConfig

            # 🔧 关键修复：强制使用空字符串，完全避免任何文件加载和file://协议
            CurrentConfig.ONLINE_HOST = ""
            print("✅ pyecharts已强制配置为完全离线模式（无文件加载）")

            # 设置其他离线相关配置
            try:
                # 如果有其他全局配置，也设置为离线模式
                CurrentConfig.ASSETS_HOST = ""
                print("✅ pyecharts资源主机已设置为离线模式")
            except:
                pass  # 某些版本可能没有这个配置

            # 🔧 新增：确保配置立即生效，防止后续被覆盖
            import atexit
            def ensure_offline_on_exit():
                try:
                    CurrentConfig.ONLINE_HOST = ""
                    CurrentConfig.ASSETS_HOST = ""
                except:
                    pass
            atexit.register(ensure_offline_on_exit)

        except ImportError:
            print("⚠️ pyecharts未安装，跳过配置")
        except Exception as e:
            print(f"⚠️ pyecharts配置失败: {e}")

        return True

    except Exception as e:
        print(f"❌ 离线环境配置失败: {e}")
        return False

# 🔧 立即执行离线环境配置
print("🔧 正在配置离线环境...")
configure_offline_environment()

# 🔧 关键修复：在导入Qt之前设置最强力的WebEngine禁用
def force_disable_webengine_network():
    """强制禁用WebEngine的所有网络功能"""
    import os

    # 设置最强力的环境变量
    webengine_env = {
        'QTWEBENGINE_DISABLE_SANDBOX': '1',
        'QTWEBENGINE_DISABLE_GPU_SANDBOX': '1',
        'QTWEBENGINE_REMOTE_DEBUGGING': '0',
        'QTWEBENGINE_DISABLE_NETWORK': '1',
        'QTWEBENGINE_DISABLE_BACKGROUND_NETWORKING': '1',
        'QTWEBENGINE_DISABLE_SYNC': '1',
        'QTWEBENGINE_DISABLE_GPU': '1',
        'QTWEBENGINE_DISABLE_SOFTWARE_RASTERIZER': '1',
        'QTWEBENGINE_DISABLE_ACCELERATED_2D_CANVAS': '1',
        'QTWEBENGINE_DISABLE_WEBGL': '1',
        'QTWEBENGINE_DISABLE_EXTENSIONS': '1',
        'QTWEBENGINE_DISABLE_PLUGINS': '1',
        'QTWEBENGINE_DISABLE_WEB_SECURITY': '1',
        'QTWEBENGINE_DISABLE_FEATURES': 'VizDisplayCompositor,TranslateUI,MediaRouter,PasswordManager,AutofillServerCommunication',
        'QTWEBENGINE_CHROMIUM_FLAGS': '--no-sandbox --disable-web-security --disable-features=VizDisplayCompositor --disable-gpu --disable-software-rasterizer --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-renderer-backgrounding --disable-extensions --disable-sync --no-first-run --disable-logging --disable-background-networking --in-process-gpu --disable-dev-shm-usage --disable-network-service --disable-background-downloads --disable-component-update --disable-domain-reliability --disable-client-side-phishing-detection --disable-hang-monitor --disable-prompt-on-repost --disable-default-apps --disable-component-extensions-with-background-pages --disable-background-mode --no-default-browser-check --no-pings --disable-translate --disable-ipc-flooding-protection'
    }

    for key, value in webengine_env.items():
        os.environ[key] = value

    print("✅ 强制WebEngine网络禁用已设置")

# 立即执行WebEngine网络禁用
force_disable_webengine_network()

def setup_multiprocessing():
    """设置多进程支持"""
    # 设置多进程启动方法（Windows兼容）
    if sys.platform.startswith('win'):
        multiprocessing.set_start_method('spawn', force=True)

    # 设置进程池大小为CPU核心数
    cpu_count = multiprocessing.cpu_count()
    os.environ['OMP_NUM_THREADS'] = str(cpu_count)
    os.environ['MKL_NUM_THREADS'] = str(cpu_count)
    os.environ['NUMEXPR_NUM_THREADS'] = str(cpu_count)

    return cpu_count

def preload_modules_async():
    """异步预加载模块"""
    modules_to_preload = [
        'PyQt5.QtWidgets',
        'PyQt5.QtCore',
        'PyQt5.QtGui',
        'pandas',
        'numpy'
    ]

    def load_module(module_name):
        try:
            __import__(module_name)
        except ImportError:
            pass  # 忽略导入失败的模块

    # 使用线程池并行加载模块
    with ThreadPoolExecutor(max_workers=4) as executor:
        executor.map(load_module, modules_to_preload)

if __name__ == "__main__":
    try:
        # 1. 设置多核处理器支持
        cpu_count = setup_multiprocessing()

        # 2. 设置环境变量
        os.environ["PYTHONIOENCODING"] = "utf-8"

        # 3. 修复WebEngine网络服务崩溃问题（终极版 - 最简单有效的设置）
        os.environ["QTWEBENGINE_DISABLE_SANDBOX"] = "1"
        os.environ["QTWEBENGINE_DISABLE_GPU_SANDBOX"] = "1"
        os.environ["QTWEBENGINE_REMOTE_DEBUGGING"] = "0"

        # 使用经过验证的最有效Chromium标志组合
        os.environ["QTWEBENGINE_CHROMIUM_FLAGS"] = "--no-sandbox --disable-web-security --disable-features=VizDisplayCompositor --disable-gpu --disable-software-rasterizer --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-renderer-backgrounding --disable-extensions --disable-sync --no-first-run --disable-logging --disable-background-networking --in-process-gpu --disable-dev-shm-usage"

        # 设置本地数据目录
        project_root = os.path.dirname(os.path.abspath(__file__))
        webengine_data_dir = os.path.join(project_root, "temp", "webengine_data")
        os.makedirs(webengine_data_dir, exist_ok=True)
        os.environ["QTWEBENGINE_USER_DATA_DIR"] = webengine_data_dir

        # 4. 创建配置目录
        config_dir = os.path.join(os.getcwd(), "app")
        os.makedirs(config_dir, exist_ok=True)

        # 5. 异步预加载模块（利用多核）
        preload_thread = threading.Thread(target=preload_modules_async)
        preload_thread.start()

        # 6. 初始化日志系统（优化启动性能）
        from src.utils.logging_config import setup_logging
        logger = setup_logging(log_level=logging.WARNING, optimize_startup=True)
        # logger.info("从run.py启动应用程序")  # 注释掉不必要的启动信息

        # 7. 等待模块预加载完成
        preload_thread.join(timeout=2.0)  # 最多等待2秒

        # 8. 导入和定义必要的组件
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt

        # 9. 设置WebEngine所需的OpenGL上下文共享（必须在QApplication创建之前）
        QApplication.setAttribute(Qt.AA_ShareOpenGLContexts)

        # 重新创建SplashScreen类
        from PyQt5.QtWidgets import QSplashScreen, QLabel, QProgressBar
        from PyQt5.QtGui import QPixmap, QFont, QIcon
        from PyQt5.QtCore import QTimer, QSize
        from src.utils.resource_loader import ResourceLoader

        class SplashScreen(QSplashScreen):
            """启动屏幕类"""
            
            def __init__(self):
                super().__init__()
                
                # 加载启动图像
                pixmap = ResourceLoader.load_pixmap('splash.png')
                if pixmap.isNull():
                    # 如果没有找到图像，创建一个纯色背景
                    pixmap = QPixmap(600, 400)
                    pixmap.fill(Qt.white)
                
                self.setPixmap(pixmap)
                
                # 获取图像尺寸
                width = pixmap.width()
                height = pixmap.height()
                
                # 创建进度条 - 放在底部红框区域
                progress_width = int(width * 0.8)  # 进度条宽度为图像宽度的80%
                progress_height = 40  # 进度条高度
                progress_x = int((width - progress_width) / 2)  # 居中显示
                progress_y = int(height * 0.92)  # 放在底部接近红框位置
                
                self.progress = QProgressBar(self)
                self.progress.setGeometry(progress_x, progress_y, progress_width, progress_height)
                self.progress.setStyleSheet("""
                    QProgressBar {
                        border: none;
                        background-color: rgba(255, 255, 255, 30);
                        border-radius: 5px;
                        text-align: center;
                    }
                    QProgressBar::chunk {
                        background-color: #4DD0E1;
                        border-radius: 5px;
                    }
                """)
                self.progress.setTextVisible(False)  # 不显示进度文本
                
                # 创建加载文本标签 - 根据进度动态显示
                self.loading_label = QLabel(self)
                self.loading_label.setGeometry(progress_x, progress_y - 80, progress_width, 55)
                self.loading_label.setAlignment(Qt.AlignCenter)
                self.loading_label.setStyleSheet("color: white; font-size: 32px;")
                self.loading_label.setText("正在初始化...")
                
                # 设置进度值
                self.progress.setValue(0)
                
                # 进度文本数组
                self.progress_texts = [
                    "正在初始化...",
                    "加载系统组件...",
                    "配置系统环境...",
                    "准备系统资源...",
                    "检查系统完整性...",
                    "即将进入系统..."
                ]
                
                # 创建定时器模拟加载过程
                self.timer = QTimer(self)
                self.timer.timeout.connect(self.update_progress)
                self.timer.start(40)  # 稍微加快加载速度
                
                self.setWindowFlag(Qt.WindowStaysOnTopHint)
                self.show()
            
            def update_progress(self):
                """更新进度条并根据进度更新文本"""
                current = self.progress.value()
                
                if current >= 100:
                    self.timer.stop()
                    return
                
                # 更新进度
                new_value = current + 1
                self.progress.setValue(new_value)
                
                # 根据进度更新文本
                text_index = min(new_value // 20, len(self.progress_texts) - 1)
                self.loading_label.setText(self.progress_texts[text_index])
                    
        # 创建应用
        app = QApplication(sys.argv)

        # 配置WebEngine全局设置（防止网络服务崩溃的关键设置）
        try:
            from PyQt5.QtWebEngineWidgets import QWebEngineSettings
            settings = QWebEngineSettings.globalSettings()

            # 最关键的设置 - 禁用所有网络相关功能
            settings.setAttribute(QWebEngineSettings.LocalContentCanAccessRemoteUrls, False)
            settings.setAttribute(QWebEngineSettings.XSSAuditingEnabled, False)
            settings.setAttribute(QWebEngineSettings.JavascriptCanOpenWindows, False)
            settings.setAttribute(QWebEngineSettings.PluginsEnabled, False)
            settings.setAttribute(QWebEngineSettings.Accelerated2dCanvasEnabled, False)
            settings.setAttribute(QWebEngineSettings.WebGLEnabled, False)

            # 保持必要的本地功能
            settings.setAttribute(QWebEngineSettings.JavascriptEnabled, True)
            settings.setAttribute(QWebEngineSettings.LocalContentCanAccessFileUrls, True)
            settings.setAttribute(QWebEngineSettings.LocalStorageEnabled, True)
            settings.setAttribute(QWebEngineSettings.AutoLoadImages, True)

        except ImportError:
            pass  # PyQtWebEngine不可用时静默跳过
        except Exception:
            pass  # 配置失败时静默跳过，不影响程序启动

        # 设置应用程序名称和版本（用于Windows任务栏识别）
        app.setApplicationName("试验问题管理控制工具")
        app.setApplicationVersion("1.0")
        app.setOrganizationName("AVTG")

        # 确保资源目录存在
        ResourceLoader.create_resource_directories()

        # 设置应用程序级别的图标（用于Windows任务栏）
        try:
            # Windows特定：必须在设置图标之前设置应用程序用户模型ID
            import platform
            if platform.system() == 'Windows':
                try:
                    import ctypes
                    # 设置应用程序用户模型ID，确保Windows任务栏正确显示图标
                    app_id = "AVTG.TrialProblemManagementTool.1.0"
                    ctypes.windll.shell32.SetCurrentProcessExplicitAppUserModelID(app_id)
                    # logger.info(f"Windows任务栏应用程序ID设置成功: {app_id}")  # 注释掉不必要的信息
                except Exception as e:
                    # logger.warning(f"设置Windows任务栏应用程序ID失败: {e}")  # 注释掉不必要的警告
                    pass

            # 优先使用ICO格式图标
            ico_path = os.path.join('src', 'resources', 'images', 'logo.ico')
            png_path = os.path.join('src', 'resources', 'images', 'logo.png')

            app_icon = None
            icon_path_used = None

            # 检查并加载ICO图标
            if os.path.exists(ico_path):
                try:
                    app_icon = QIcon(ico_path)
                    if not app_icon.isNull():
                        icon_path_used = ico_path
                        # logger.info(f"成功加载ICO格式应用程序图标: {ico_path}")  # 注释掉不必要的信息
                        # 检查ICO图标的可用尺寸
                        sizes = app_icon.availableSizes()
                        # logger.info(f"ICO图标可用尺寸: {[f'{s.width()}x{s.height()}' for s in sizes]}")  # 注释掉不必要的信息
                    else:
                        # logger.warning(f"ICO图标文件无效: {ico_path}")  # 注释掉不必要的警告
                        pass
                except Exception as e:
                    # logger.error(f"加载ICO图标失败: {e}")  # 注释掉不必要的错误信息
                    pass

            # 如果ICO加载失败，尝试PNG
            if not app_icon or app_icon.isNull():
                if os.path.exists(png_path):
                    try:
                        app_icon = QIcon(png_path)
                        if not app_icon.isNull():
                            icon_path_used = png_path
                            # logger.info(f"成功加载PNG格式应用程序图标: {png_path}")  # 注释掉不必要的信息
                        else:
                            # logger.warning(f"PNG图标文件无效: {png_path}")  # 注释掉不必要的警告
                            pass
                    except Exception as e:
                        # logger.error(f"加载PNG图标失败: {e}")  # 注释掉不必要的错误信息
                        pass

            if app_icon and not app_icon.isNull():
                # 设置应用程序图标
                app.setWindowIcon(app_icon)
                # logger.info(f"应用程序图标设置成功，使用文件: {icon_path_used}")  # 注释掉不必要的信息

                # 额外的Windows特定设置
                if platform.system() == 'Windows':
                    try:
                        # 创建多尺寸图标以确保任务栏显示正确
                        from PyQt5.QtCore import QSize
                        # 确保图标包含常用的Windows尺寸
                        required_sizes = [QSize(16, 16), QSize(32, 32), QSize(48, 48), QSize(64, 64)]
                        for size in required_sizes:
                            pixmap = app_icon.pixmap(size)
                            if not pixmap.isNull():
                                # logger.info(f"图标包含尺寸: {size.width()}x{size.height()}")  # 注释掉不必要的信息
                                pass
                    except Exception as e:
                        # logger.warning(f"检查图标尺寸失败: {e}")  # 注释掉不必要的警告
                        pass
            else:
                # logger.warning("应用程序图标加载失败，将使用默认图标")  # 注释掉不必要的警告
                # logger.warning(f"ICO文件存在: {os.path.exists(ico_path)}")  # 注释掉不必要的警告
                # logger.warning(f"PNG文件存在: {os.path.exists(png_path)}")  # 注释掉不必要的警告
                pass
        except Exception as e:
            # logger.error(f"设置应用程序图标失败: {e}")  # 注释掉不必要的错误信息
            # import traceback
            # logger.error(f"详细错误: {traceback.format_exc()}")  # 注释掉不必要的错误信息
            pass

        # 使用线程池并行创建默认图标缓存（利用多核）
        def create_icons_async():
            icon_names = ["save", "save_as", "undo", "redo", "search", "filter"]
            with ThreadPoolExecutor(max_workers=3) as executor:
                executor.map(ResourceLoader.create_default_icon, icon_names)

        # 启动图标创建线程
        icon_thread = threading.Thread(target=create_icons_async)
        icon_thread.start()

        # 尝试加载样式表，但即使失败也继续运行
        try:
            style = ResourceLoader.load_stylesheet("default.qss")
            if style:
                app.setStyleSheet(style)
        except Exception as e:
            # logger.error(f"加载样式表失败，但将继续运行程序: {e}")  # 注释掉不必要的错误信息
            pass
        
        # 显示启动屏幕
        splash = SplashScreen()

        # 处理启动期间的事件
        app.processEvents()

        # 使用线程池并行导入和初始化组件（利用多核）
        def load_components():
            from src.views.main_window import MainWindow
            from src.controllers.main_controller import MainController
            from src.utils.config import Config
            return MainWindow, MainController, Config

        # 在后台线程中加载组件
        import_thread = threading.Thread(target=load_components)
        import_thread.start()

        # 等待导入完成
        import_thread.join()

        # 现在导入已完成，可以使用组件
        from src.views.main_window import MainWindow
        from src.controllers.main_controller import MainController
        from src.utils.config import Config

        # 初始化配置
        config = Config()

        # 创建主窗口
        main_window = MainWindow(controller=None)

        # 创建控制器
        controller = MainController(main_window)

        # 将控制器存储在主窗口中，以便访问
        main_window._controller = controller

        # 等待图标创建完成
        icon_thread.join(timeout=1.0)  # 最多等待1秒
        
        # 连接窗口事件到控制器方法
        def connect_window_to_controller(window, controller):
            """连接窗口事件到控制器方法"""
            # 项目操作
            if hasattr(window, 'open_project_action'):
                window.open_project_action.triggered.connect(controller.open_project)
            if hasattr(window, 'new_project_action'):
                window.new_project_action.triggered.connect(controller.new_project)
            if hasattr(window, 'save_project_action'):
                window.save_project_action.triggered.connect(controller.save_project)
            if hasattr(window, 'save_as_project_action'):
                window.save_as_project_action.triggered.connect(controller.save_as_project)
            if hasattr(window, 'delete_project_action'):
                window.delete_project_action.triggered.connect(controller.delete_project)
            
            # 文件操作
            if hasattr(window, 'open_file_action'):
                window.open_file_action.triggered.connect(controller.open_file)
            if hasattr(window, 'new_file_action'):
                window.new_file_action.triggered.connect(controller.new_file)
            if hasattr(window, 'delete_file_action'):
                window.delete_file_action.triggered.connect(controller.delete_file)
            
            # 导入导出操作
            if hasattr(window, 'import_problem_template_action'):
                window.import_problem_template_action.triggered.connect(controller.import_template)
            if hasattr(window, 'export_file_action'):
                window.export_file_action.triggered.connect(controller.export_file)
            
            # 筛选和搜索操作
            if hasattr(window, 'search_action') and hasattr(window, 'find'):
                window.search_action.triggered.connect(window.find)
            
            if hasattr(window, 'filter_action') and hasattr(window, 'filter'):
                window.filter_action.triggered.connect(window.filter)
            
            # 问题操作 - 右键菜单事件已在main_window.py的_init_problem_table方法中绑定，此处不需要重复绑定
            # if hasattr(window, 'problem_table') and hasattr(window, 'show_context_menu'):
            #     window.problem_table.customContextMenuRequested.connect(window.show_context_menu)
            
            # 导出问题
            if hasattr(window, 'export_problems_action') and hasattr(window, 'export_problems'):
                window.export_problems_action.triggered.connect(window.export_problems)
        
        # 连接主窗口和控制器
        connect_window_to_controller(main_window, controller)
        
        # 启动屏幕结束后显示主窗口
        splash.finish(main_window)
        main_window.show()
        
        # 运行应用
        sys.exit(app.exec_())
        
    except ImportError as e:
        print(f"错误：无法导入所需模块: {e}")
        # traceback.print_exc()  # 注释掉详细错误信息，减少输出
        sys.exit(1)
    except Exception as e:
        print(f"程序异常终止: {e}")
        if 'logger' in locals():
            # logger.critical(f"程序异常终止: {e}", exc_info=True)  # 注释掉不必要的日志
            pass
        # traceback.print_exc()  # 注释掉详细错误信息，减少输出
        sys.exit(1)