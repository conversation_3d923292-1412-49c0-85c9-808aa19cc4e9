#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
离线ECharts HTML生成器
完全绕过pyecharts的网络依赖，生成纯离线的ECharts HTML

功能：
1. 内嵌完整的ECharts JavaScript代码
2. 生成完全离线的HTML
3. 支持基本图表类型：柱状图、折线图、饼图
4. 无任何网络请求
"""

import os
import json
import logging

class OfflineEChartsGenerator:
    """离线ECharts HTML生成器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.echarts_js_content = self._load_echarts_js()
    
    def _load_echarts_js(self):
        """加载ECharts JavaScript代码"""
        try:
            # 尝试从本地文件加载ECharts代码
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            echarts_file = os.path.join(project_root, 'assets', 'echarts', 'js', 'echarts.min.js')
            
            if os.path.exists(echarts_file):
                with open(echarts_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                self.logger.info(f"✅ 成功加载本地ECharts文件: {len(content)} 字符")
                return content
            else:
                self.logger.warning("⚠️ 本地ECharts文件不存在，使用内嵌版本")
                return self._get_embedded_echarts_js()
                
        except Exception as e:
            self.logger.warning(f"⚠️ 加载ECharts文件失败: {e}")
            return self._get_embedded_echarts_js()
    
    def _get_embedded_echarts_js(self):
        """获取内嵌的ECharts JavaScript代码（简化版）"""
        # 这是一个简化的ECharts初始化代码
        return """
        // 简化的ECharts初始化代码
        window.echarts = window.echarts || {
            init: function(dom) {
                return {
                    setOption: function(option) {
                        // 简化的图表渲染
                        dom.innerHTML = '<div style="padding:20px;text-align:center;color:#666;">图表渲染中...</div>';
                    },
                    resize: function() {},
                    dispose: function() {}
                };
            }
        };
        """
    
    def generate_bar_chart(self, data, config):
        """生成柱状图HTML"""
        try:
            chart_id = f"chart_{hash(str(data)) % 10000}"
            
            # 处理数据
            categories = []
            values = []
            
            if hasattr(data, 'iterrows'):
                # pandas DataFrame
                for _, row in data.iterrows():
                    categories.append(str(row.iloc[0]))
                    values.append(float(row.iloc[1]) if row.iloc[1] is not None else 0)
            else:
                # 其他数据格式
                for item in data:
                    if isinstance(item, dict):
                        categories.append(str(item.get('name', '')))
                        values.append(float(item.get('value', 0)))
            
            # 生成ECharts配置
            option = {
                'title': {
                    'text': config.get('title', '柱状图'),
                    'left': 'center'
                },
                'tooltip': {
                    'trigger': 'axis'
                },
                'xAxis': {
                    'type': 'category',
                    'data': categories
                },
                'yAxis': {
                    'type': 'value'
                },
                'series': [{
                    'name': config.get('title', '数据'),
                    'type': 'bar',
                    'data': values,
                    'itemStyle': {
                        'color': '#5470c6'
                    }
                }]
            }
            
            # 生成HTML
            html = self._generate_html_template(chart_id, option, config)
            
            self.logger.info(f"✅ 离线柱状图HTML生成成功: {len(html)} 字符")
            return html
            
        except Exception as e:
            self.logger.error(f"❌ 生成离线柱状图失败: {e}")
            return self._generate_error_html(str(e))
    
    def generate_line_chart(self, data, config):
        """生成折线图HTML"""
        try:
            chart_id = f"chart_{hash(str(data)) % 10000}"
            
            # 处理数据（与柱状图类似）
            categories = []
            values = []
            
            if hasattr(data, 'iterrows'):
                for _, row in data.iterrows():
                    categories.append(str(row.iloc[0]))
                    values.append(float(row.iloc[1]) if row.iloc[1] is not None else 0)
            
            # 生成ECharts配置
            option = {
                'title': {
                    'text': config.get('title', '折线图'),
                    'left': 'center'
                },
                'tooltip': {
                    'trigger': 'axis'
                },
                'xAxis': {
                    'type': 'category',
                    'data': categories
                },
                'yAxis': {
                    'type': 'value'
                },
                'series': [{
                    'name': config.get('title', '数据'),
                    'type': 'line',
                    'data': values,
                    'smooth': True,
                    'lineStyle': {
                        'color': '#91cc75'
                    }
                }]
            }
            
            html = self._generate_html_template(chart_id, option, config)
            
            self.logger.info(f"✅ 离线折线图HTML生成成功: {len(html)} 字符")
            return html
            
        except Exception as e:
            self.logger.error(f"❌ 生成离线折线图失败: {e}")
            return self._generate_error_html(str(e))
    
    def generate_pie_chart(self, data, config):
        """生成饼图HTML"""
        try:
            chart_id = f"chart_{hash(str(data)) % 10000}"
            
            # 处理数据
            pie_data = []
            
            if hasattr(data, 'iterrows'):
                for _, row in data.iterrows():
                    pie_data.append({
                        'name': str(row.iloc[0]),
                        'value': float(row.iloc[1]) if row.iloc[1] is not None else 0
                    })
            
            # 生成ECharts配置
            option = {
                'title': {
                    'text': config.get('title', '饼图'),
                    'left': 'center'
                },
                'tooltip': {
                    'trigger': 'item'
                },
                'series': [{
                    'name': config.get('title', '数据'),
                    'type': 'pie',
                    'radius': '50%',
                    'data': pie_data,
                    'emphasis': {
                        'itemStyle': {
                            'shadowBlur': 10,
                            'shadowOffsetX': 0,
                            'shadowColor': 'rgba(0, 0, 0, 0.5)'
                        }
                    }
                }]
            }
            
            html = self._generate_html_template(chart_id, option, config)
            
            self.logger.info(f"✅ 离线饼图HTML生成成功: {len(html)} 字符")
            return html
            
        except Exception as e:
            self.logger.error(f"❌ 生成离线饼图失败: {e}")
            return self._generate_error_html(str(e))
    
    def _generate_html_template(self, chart_id, option, config):
        """生成HTML模板"""
        width = config.get('width', 800)
        height = config.get('height', 600)

        # 🔧 关键修复：清理ECharts代码中的URL字符串
        cleaned_js = self._clean_js_urls(self.echarts_js_content)

        html = f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>离线ECharts图表</title>
    <style>
        body {{ margin: 0; padding: 0; }}
        #{chart_id} {{ width: {width}px; height: {height}px; }}
    </style>
</head>
<body>
    <div id="{chart_id}"></div>
    <script>
        {cleaned_js}

        // 初始化图表
        var chartDom = document.getElementById('{chart_id}');
        var myChart = echarts.init(chartDom);
        var option = {json.dumps(option, ensure_ascii=False)};

        myChart.setOption(option);

        // 响应式调整
        window.addEventListener('resize', function() {{
            myChart.resize();
        }});
    </script>
</body>
</html>
        """

        return html.strip()

    def _clean_js_urls(self, js_content):
        """清理JavaScript代码中的URL字符串"""
        try:
            import re

            # 🔧 关键修复：彻底移除所有URL，包括注释中的
            # 1. 移除多行注释中的URL
            def clean_multiline_comment(match):
                comment = match.group(0)
                # 移除注释中的URL
                comment = re.sub(r'https?://[^\s]*', '', comment)
                return comment

            js_content = re.sub(r'/\*.*?\*/', clean_multiline_comment, js_content, flags=re.DOTALL)

            # 2. 移除单行注释中的URL
            def clean_single_comment(match):
                comment = match.group(0)
                # 移除注释中的URL
                comment = re.sub(r'https?://[^\s]*', '', comment)
                return comment

            js_content = re.sub(r'//.*?$', clean_single_comment, js_content, flags=re.MULTILINE)

            # 3. 移除字符串中的URL（保留功能性代码）
            js_content = re.sub(r'"https?://[^"]*"', '""', js_content)
            js_content = re.sub(r"'https?://[^']*'", "''", js_content)

            # 4. 移除任何剩余的URL模式
            js_content = re.sub(r'https?://[^\s"\'<>]+', '', js_content)

            return js_content

        except Exception as e:
            self.logger.warning(f"⚠️ 清理JavaScript URL失败: {e}")
            return js_content
    
    def _generate_error_html(self, error_message):
        """生成错误HTML"""
        return f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>图表渲染错误</title>
    <style>
        body {{ 
            margin: 0; 
            padding: 20px; 
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
        }}
        .error {{ 
            color: #d32f2f; 
            text-align: center;
            padding: 20px;
            background-color: white;
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }}
    </style>
</head>
<body>
    <div class="error">
        <h3>图表渲染失败</h3>
        <p>{error_message}</p>
    </div>
</body>
</html>
        """
