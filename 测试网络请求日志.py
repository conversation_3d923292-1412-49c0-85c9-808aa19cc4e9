#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试网络请求日志工具
快速测试图表渲染时是否会输出网络请求日志
"""

import os
import sys

# 设置项目根目录
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

def test_chart_network_logging():
    """测试图表渲染的网络请求日志"""
    print("=" * 60)
    print("测试图表渲染网络请求日志")
    print("=" * 60)
    
    try:
        # 导入必要的模块
        from src.utils.chart_renderer import ChartRenderer
        import pandas as pd
        
        # 创建图表渲染器
        print("🔧 创建图表渲染器...")
        renderer = ChartRenderer()
        
        # 创建测试数据
        test_data = pd.DataFrame({
            'category': ['网络测试A', '网络测试B', '网络测试C'],
            'value': [10, 20, 15]
        })
        
        # 测试图表配置
        config = {
            'chart_type': '柱状图',
            'title': '网络请求日志测试图表',
            'width': 400,
            'height': 300,
            'x_field': 'category',
            'y_field': 'value',
            'agg_func': '求和'
        }
        
        print("🔧 开始渲染图表...")
        print("注意观察下面是否有网络请求日志输出:")
        print("-" * 40)
        
        # 渲染图表
        html = renderer.render_chart(config, test_data)
        
        print("-" * 40)
        print("🔧 图表渲染完成")
        
        if html and len(html) > 100:
            print(f"✅ 图表HTML生成成功，长度: {len(html)} 字符")
            
            # 保存测试文件
            test_file = os.path.join(project_root, 'temp', 'network_log_test.html')
            os.makedirs(os.path.dirname(test_file), exist_ok=True)
            
            with open(test_file, 'w', encoding='utf-8') as f:
                f.write(html)
            
            print(f"📁 测试文件已保存: {test_file}")
            
            # 手动检查网络请求
            network_count = html.count('https://') + html.count('http://') + html.count('file://')
            if network_count > 0:
                print(f"⚠️ HTML中仍包含 {network_count} 个网络链接")
            else:
                print("✅ HTML中未发现网络链接")
        else:
            print("❌ 图表HTML生成失败")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("网络请求日志测试工具")
    print("目的：验证图表渲染时是否会输出网络请求日志到控制台")
    print()
    
    test_chart_network_logging()
    
    print()
    print("=" * 60)
    print("测试完成")
    print("=" * 60)
    print("如果上面有 🚨 网络请求日志输出，说明找到了网络请求源头")
    print("如果没有网络请求日志，说明图表渲染是完全离线的")
    print()
    input("按回车键退出...")

if __name__ == "__main__":
    main()
